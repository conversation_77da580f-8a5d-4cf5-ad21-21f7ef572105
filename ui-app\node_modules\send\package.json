{"name": "send", "description": "Better streaming static file server with Range and conditional-GET support", "version": "1.2.0", "author": "<PERSON><PERSON> <<EMAIL>>", "contributors": ["<PERSON> <<EMAIL>>", "<PERSON> <<EMAIL>>", "<PERSON><PERSON><PERSON> <<EMAIL>>"], "license": "MIT", "repository": "pillarjs/send", "keywords": ["static", "file", "server"], "dependencies": {"debug": "^4.3.5", "encodeurl": "^2.0.0", "escape-html": "^1.0.3", "etag": "^1.8.1", "fresh": "^2.0.0", "http-errors": "^2.0.0", "mime-types": "^3.0.1", "ms": "^2.1.3", "on-finished": "^2.4.1", "range-parser": "^1.2.1", "statuses": "^2.0.1"}, "devDependencies": {"after": "^0.8.2", "eslint": "7.32.0", "eslint-config-standard": "14.1.1", "eslint-plugin-import": "2.25.4", "eslint-plugin-markdown": "2.2.1", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "5.2.0", "eslint-plugin-standard": "4.1.0", "mocha": "^10.7.0", "nyc": "^17.0.0", "supertest": "6.2.2"}, "files": ["HISTORY.md", "LICENSE", "README.md", "index.js"], "engines": {"node": ">= 18"}, "scripts": {"lint": "eslint .", "test": "mocha --check-leaks --reporter spec", "test-ci": "nyc --reporter=lcovonly --reporter=text npm test", "test-cov": "nyc --reporter=html --reporter=text npm test", "version": "node scripts/version-history.js && git add HISTORY.md"}}