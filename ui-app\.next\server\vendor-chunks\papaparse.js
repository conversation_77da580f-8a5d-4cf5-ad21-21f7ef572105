/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
exports.id = "vendor-chunks/papaparse";
exports.ids = ["vendor-chunks/papaparse"];
exports.modules = {

/***/ "(ssr)/./node_modules/papaparse/papaparse.js":
/*!*********************************************!*\
  !*** ./node_modules/papaparse/papaparse.js ***!
  \*********************************************/
/***/ (function(module, exports, __webpack_require__) {

eval("var __WEBPACK_AMD_DEFINE_FACTORY__, __WEBPACK_AMD_DEFINE_ARRAY__, __WEBPACK_AMD_DEFINE_RESULT__;/* @license\nPapa Parse\nv5.5.3\nhttps://github.com/mholt/PapaParse\nLicense: MIT\n*/\n\n(function(root, factory)\n{\n\t/* globals define */\n\tif (true)\n\t{\n\t\t// AMD. Register as an anonymous module.\n\t\t!(__WEBPACK_AMD_DEFINE_ARRAY__ = [], __WEBPACK_AMD_DEFINE_FACTORY__ = (factory),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ = (typeof __WEBPACK_AMD_DEFINE_FACTORY__ === 'function' ?\n\t\t(__WEBPACK_AMD_DEFINE_FACTORY__.apply(exports, __WEBPACK_AMD_DEFINE_ARRAY__)) : __WEBPACK_AMD_DEFINE_FACTORY__),\n\t\t__WEBPACK_AMD_DEFINE_RESULT__ !== undefined && (module.exports = __WEBPACK_AMD_DEFINE_RESULT__));\n\t}\n\telse {}\n\t// in strict mode we cannot access arguments.callee, so we need a named reference to\n\t// stringify the factory method for the blob worker\n\t// eslint-disable-next-line func-name\n}(this, function moduleFactory()\n{\n\t'use strict';\n\n\tvar global = (function() {\n\t\t// alternative method, similar to `Function('return this')()`\n\t\t// but without using `eval` (which is disabled when\n\t\t// using Content Security Policy).\n\n\t\tif (typeof self !== 'undefined') { return self; }\n\t\tif (typeof window !== 'undefined') { return window; }\n\t\tif (typeof global !== 'undefined') { return global; }\n\n\t\t// When running tests none of the above have been defined\n\t\treturn {};\n\t})();\n\n\n\tfunction getWorkerBlob() {\n\t\tvar URL = global.URL || global.webkitURL || null;\n\t\tvar code = moduleFactory.toString();\n\t\treturn Papa.BLOB_URL || (Papa.BLOB_URL = URL.createObjectURL(new Blob([\"var global = (function() { if (typeof self !== 'undefined') { return self; } if (typeof window !== 'undefined') { return window; } if (typeof global !== 'undefined') { return global; } return {}; })(); global.IS_PAPA_WORKER=true; \", '(', code, ')();'], {type: 'text/javascript'})));\n\t}\n\n\tvar IS_WORKER = !global.document && !!global.postMessage,\n\t\tIS_PAPA_WORKER = global.IS_PAPA_WORKER || false;\n\n\tvar workers = {}, workerIdCounter = 0;\n\n\tvar Papa = {};\n\n\tPapa.parse = CsvToJson;\n\tPapa.unparse = JsonToCsv;\n\n\tPapa.RECORD_SEP = String.fromCharCode(30);\n\tPapa.UNIT_SEP = String.fromCharCode(31);\n\tPapa.BYTE_ORDER_MARK = '\\ufeff';\n\tPapa.BAD_DELIMITERS = ['\\r', '\\n', '\"', Papa.BYTE_ORDER_MARK];\n\tPapa.WORKERS_SUPPORTED = !IS_WORKER && !!global.Worker;\n\tPapa.NODE_STREAM_INPUT = 1;\n\n\t// Configurable chunk sizes for local and remote files, respectively\n\tPapa.LocalChunkSize = 1024 * 1024 * 10;\t// 10 MB\n\tPapa.RemoteChunkSize = 1024 * 1024 * 5;\t// 5 MB\n\tPapa.DefaultDelimiter = ',';\t\t\t// Used if not specified and detection fails\n\n\t// Exposed for testing and development only\n\tPapa.Parser = Parser;\n\tPapa.ParserHandle = ParserHandle;\n\tPapa.NetworkStreamer = NetworkStreamer;\n\tPapa.FileStreamer = FileStreamer;\n\tPapa.StringStreamer = StringStreamer;\n\tPapa.ReadableStreamStreamer = ReadableStreamStreamer;\n\tif (typeof PAPA_BROWSER_CONTEXT === 'undefined') {\n\t\tPapa.DuplexStreamStreamer = DuplexStreamStreamer;\n\t}\n\n\tif (global.jQuery)\n\t{\n\t\tvar $ = global.jQuery;\n\t\t$.fn.parse = function(options)\n\t\t{\n\t\t\tvar config = options.config || {};\n\t\t\tvar queue = [];\n\n\t\t\tthis.each(function(idx)\n\t\t\t{\n\t\t\t\tvar supported = $(this).prop('tagName').toUpperCase() === 'INPUT'\n\t\t\t\t\t\t\t\t&& $(this).attr('type').toLowerCase() === 'file'\n\t\t\t\t\t\t\t\t&& global.FileReader;\n\n\t\t\t\tif (!supported || !this.files || this.files.length === 0)\n\t\t\t\t\treturn true;\t// continue to next input element\n\n\t\t\t\tfor (var i = 0; i < this.files.length; i++)\n\t\t\t\t{\n\t\t\t\t\tqueue.push({\n\t\t\t\t\t\tfile: this.files[i],\n\t\t\t\t\t\tinputElem: this,\n\t\t\t\t\t\tinstanceConfig: $.extend({}, config)\n\t\t\t\t\t});\n\t\t\t\t}\n\t\t\t});\n\n\t\t\tparseNextFile();\t// begin parsing\n\t\t\treturn this;\t\t// maintains chainability\n\n\n\t\t\tfunction parseNextFile()\n\t\t\t{\n\t\t\t\tif (queue.length === 0)\n\t\t\t\t{\n\t\t\t\t\tif (isFunction(options.complete))\n\t\t\t\t\t\toptions.complete();\n\t\t\t\t\treturn;\n\t\t\t\t}\n\n\t\t\t\tvar f = queue[0];\n\n\t\t\t\tif (isFunction(options.before))\n\t\t\t\t{\n\t\t\t\t\tvar returned = options.before(f.file, f.inputElem);\n\n\t\t\t\t\tif (typeof returned === 'object')\n\t\t\t\t\t{\n\t\t\t\t\t\tif (returned.action === 'abort')\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\terror('AbortError', f.file, f.inputElem, returned.reason);\n\t\t\t\t\t\t\treturn;\t// Aborts all queued files immediately\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (returned.action === 'skip')\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tfileComplete();\t// parse the next file in the queue, if any\n\t\t\t\t\t\t\treturn;\n\t\t\t\t\t\t}\n\t\t\t\t\t\telse if (typeof returned.config === 'object')\n\t\t\t\t\t\t\tf.instanceConfig = $.extend(f.instanceConfig, returned.config);\n\t\t\t\t\t}\n\t\t\t\t\telse if (returned === 'skip')\n\t\t\t\t\t{\n\t\t\t\t\t\tfileComplete();\t// parse the next file in the queue, if any\n\t\t\t\t\t\treturn;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\t// Wrap up the user's complete callback, if any, so that ours also gets executed\n\t\t\t\tvar userCompleteFunc = f.instanceConfig.complete;\n\t\t\t\tf.instanceConfig.complete = function(results)\n\t\t\t\t{\n\t\t\t\t\tif (isFunction(userCompleteFunc))\n\t\t\t\t\t\tuserCompleteFunc(results, f.file, f.inputElem);\n\t\t\t\t\tfileComplete();\n\t\t\t\t};\n\n\t\t\t\tPapa.parse(f.file, f.instanceConfig);\n\t\t\t}\n\n\t\t\tfunction error(name, file, elem, reason)\n\t\t\t{\n\t\t\t\tif (isFunction(options.error))\n\t\t\t\t\toptions.error({name: name}, file, elem, reason);\n\t\t\t}\n\n\t\t\tfunction fileComplete()\n\t\t\t{\n\t\t\t\tqueue.splice(0, 1);\n\t\t\t\tparseNextFile();\n\t\t\t}\n\t\t};\n\t}\n\n\n\tif (IS_PAPA_WORKER)\n\t{\n\t\tglobal.onmessage = workerThreadReceivedMessage;\n\t}\n\n\n\n\n\tfunction CsvToJson(_input, _config)\n\t{\n\t\t_config = _config || {};\n\t\tvar dynamicTyping = _config.dynamicTyping || false;\n\t\tif (isFunction(dynamicTyping)) {\n\t\t\t_config.dynamicTypingFunction = dynamicTyping;\n\t\t\t// Will be filled on first row call\n\t\t\tdynamicTyping = {};\n\t\t}\n\t\t_config.dynamicTyping = dynamicTyping;\n\n\t\t_config.transform = isFunction(_config.transform) ? _config.transform : false;\n\n\t\tif (_config.worker && Papa.WORKERS_SUPPORTED)\n\t\t{\n\t\t\tvar w = newWorker();\n\n\t\t\tw.userStep = _config.step;\n\t\t\tw.userChunk = _config.chunk;\n\t\t\tw.userComplete = _config.complete;\n\t\t\tw.userError = _config.error;\n\n\t\t\t_config.step = isFunction(_config.step);\n\t\t\t_config.chunk = isFunction(_config.chunk);\n\t\t\t_config.complete = isFunction(_config.complete);\n\t\t\t_config.error = isFunction(_config.error);\n\t\t\tdelete _config.worker;\t// prevent infinite loop\n\n\t\t\tw.postMessage({\n\t\t\t\tinput: _input,\n\t\t\t\tconfig: _config,\n\t\t\t\tworkerId: w.id\n\t\t\t});\n\n\t\t\treturn;\n\t\t}\n\n\t\tvar streamer = null;\n\t\tif (_input === Papa.NODE_STREAM_INPUT && typeof PAPA_BROWSER_CONTEXT === 'undefined')\n\t\t{\n\t\t\t// create a node Duplex stream for use\n\t\t\t// with .pipe\n\t\t\tstreamer = new DuplexStreamStreamer(_config);\n\t\t\treturn streamer.getStream();\n\t\t}\n\t\telse if (typeof _input === 'string')\n\t\t{\n\t\t\t_input = stripBom(_input);\n\t\t\tif (_config.download)\n\t\t\t\tstreamer = new NetworkStreamer(_config);\n\t\t\telse\n\t\t\t\tstreamer = new StringStreamer(_config);\n\t\t}\n\t\telse if (_input.readable === true && isFunction(_input.read) && isFunction(_input.on))\n\t\t{\n\t\t\tstreamer = new ReadableStreamStreamer(_config);\n\t\t}\n\t\telse if ((global.File && _input instanceof File) || _input instanceof Object)\t// ...Safari. (see issue #106)\n\t\t\tstreamer = new FileStreamer(_config);\n\n\t\treturn streamer.stream(_input);\n\n\t\t// Strip character from UTF-8 BOM encoded files that cause issue parsing the file\n\t\tfunction stripBom(string) {\n\t\t\tif (string.charCodeAt(0) === 0xfeff) {\n\t\t\t\treturn string.slice(1);\n\t\t\t}\n\t\t\treturn string;\n\t\t}\n\t}\n\n\n\n\n\n\n\tfunction JsonToCsv(_input, _config)\n\t{\n\t\t// Default configuration\n\n\t\t/** whether to surround every datum with quotes */\n\t\tvar _quotes = false;\n\n\t\t/** whether to write headers */\n\t\tvar _writeHeader = true;\n\n\t\t/** delimiting character(s) */\n\t\tvar _delimiter = ',';\n\n\t\t/** newline character(s) */\n\t\tvar _newline = '\\r\\n';\n\n\t\t/** quote character */\n\t\tvar _quoteChar = '\"';\n\n\t\t/** escaped quote character, either \"\" or <config.escapeChar>\" */\n\t\tvar _escapedQuote = _quoteChar + _quoteChar;\n\n\t\t/** whether to skip empty lines */\n\t\tvar _skipEmptyLines = false;\n\n\t\t/** the columns (keys) we expect when we unparse objects */\n\t\tvar _columns = null;\n\n\t\t/** whether to prevent outputting cells that can be parsed as formulae by spreadsheet software (Excel and LibreOffice) */\n\t\tvar _escapeFormulae = false;\n\n\t\tunpackConfig();\n\n\t\tvar quoteCharRegex = new RegExp(escapeRegExp(_quoteChar), 'g');\n\n\t\tif (typeof _input === 'string')\n\t\t\t_input = JSON.parse(_input);\n\n\t\tif (Array.isArray(_input))\n\t\t{\n\t\t\tif (!_input.length || Array.isArray(_input[0]))\n\t\t\t\treturn serialize(null, _input, _skipEmptyLines);\n\t\t\telse if (typeof _input[0] === 'object')\n\t\t\t\treturn serialize(_columns || Object.keys(_input[0]), _input, _skipEmptyLines);\n\t\t}\n\t\telse if (typeof _input === 'object')\n\t\t{\n\t\t\tif (typeof _input.data === 'string')\n\t\t\t\t_input.data = JSON.parse(_input.data);\n\n\t\t\tif (Array.isArray(_input.data))\n\t\t\t{\n\t\t\t\tif (!_input.fields)\n\t\t\t\t\t_input.fields = _input.meta && _input.meta.fields || _columns;\n\n\t\t\t\tif (!_input.fields)\n\t\t\t\t\t_input.fields =  Array.isArray(_input.data[0])\n\t\t\t\t\t\t? _input.fields\n\t\t\t\t\t\t: typeof _input.data[0] === 'object'\n\t\t\t\t\t\t\t? Object.keys(_input.data[0])\n\t\t\t\t\t\t\t: [];\n\n\t\t\t\tif (!(Array.isArray(_input.data[0])) && typeof _input.data[0] !== 'object')\n\t\t\t\t\t_input.data = [_input.data];\t// handles input like [1,2,3] or ['asdf']\n\t\t\t}\n\n\t\t\treturn serialize(_input.fields || [], _input.data || [], _skipEmptyLines);\n\t\t}\n\n\t\t// Default (any valid paths should return before this)\n\t\tthrow new Error('Unable to serialize unrecognized input');\n\n\n\t\tfunction unpackConfig()\n\t\t{\n\t\t\tif (typeof _config !== 'object')\n\t\t\t\treturn;\n\n\t\t\tif (typeof _config.delimiter === 'string'\n                && !Papa.BAD_DELIMITERS.filter(function(value) { return _config.delimiter.indexOf(value) !== -1; }).length)\n\t\t\t{\n\t\t\t\t_delimiter = _config.delimiter;\n\t\t\t}\n\n\t\t\tif (typeof _config.quotes === 'boolean'\n\t\t\t\t|| typeof _config.quotes === 'function'\n\t\t\t\t|| Array.isArray(_config.quotes))\n\t\t\t\t_quotes = _config.quotes;\n\n\t\t\tif (typeof _config.skipEmptyLines === 'boolean'\n\t\t\t\t|| typeof _config.skipEmptyLines === 'string')\n\t\t\t\t_skipEmptyLines = _config.skipEmptyLines;\n\n\t\t\tif (typeof _config.newline === 'string')\n\t\t\t\t_newline = _config.newline;\n\n\t\t\tif (typeof _config.quoteChar === 'string')\n\t\t\t\t_quoteChar = _config.quoteChar;\n\n\t\t\tif (typeof _config.header === 'boolean')\n\t\t\t\t_writeHeader = _config.header;\n\n\t\t\tif (Array.isArray(_config.columns)) {\n\n\t\t\t\tif (_config.columns.length === 0) throw new Error('Option columns is empty');\n\n\t\t\t\t_columns = _config.columns;\n\t\t\t}\n\n\t\t\tif (_config.escapeChar !== undefined) {\n\t\t\t\t_escapedQuote = _config.escapeChar + _quoteChar;\n\t\t\t}\n\n\t\t\tif (_config.escapeFormulae instanceof RegExp) {\n\t\t\t\t_escapeFormulae = _config.escapeFormulae;\n\t\t\t} else if (typeof _config.escapeFormulae === 'boolean' && _config.escapeFormulae) {\n\t\t\t\t_escapeFormulae =  /^[=+\\-@\\t\\r].*$/;\n\t\t\t}\n\t\t}\n\n\t\t/** The double for loop that iterates the data and writes out a CSV string including header row */\n\t\tfunction serialize(fields, data, skipEmptyLines)\n\t\t{\n\t\t\tvar csv = '';\n\n\t\t\tif (typeof fields === 'string')\n\t\t\t\tfields = JSON.parse(fields);\n\t\t\tif (typeof data === 'string')\n\t\t\t\tdata = JSON.parse(data);\n\n\t\t\tvar hasHeader = Array.isArray(fields) && fields.length > 0;\n\t\t\tvar dataKeyedByField = !(Array.isArray(data[0]));\n\n\t\t\t// If there a header row, write it first\n\t\t\tif (hasHeader && _writeHeader)\n\t\t\t{\n\t\t\t\tfor (var i = 0; i < fields.length; i++)\n\t\t\t\t{\n\t\t\t\t\tif (i > 0)\n\t\t\t\t\t\tcsv += _delimiter;\n\t\t\t\t\tcsv += safe(fields[i], i);\n\t\t\t\t}\n\t\t\t\tif (data.length > 0)\n\t\t\t\t\tcsv += _newline;\n\t\t\t}\n\n\t\t\t// Then write out the data\n\t\t\tfor (var row = 0; row < data.length; row++)\n\t\t\t{\n\t\t\t\tvar maxCol = hasHeader ? fields.length : data[row].length;\n\n\t\t\t\tvar emptyLine = false;\n\t\t\t\tvar nullLine = hasHeader ? Object.keys(data[row]).length === 0 : data[row].length === 0;\n\t\t\t\tif (skipEmptyLines && !hasHeader)\n\t\t\t\t{\n\t\t\t\t\temptyLine = skipEmptyLines === 'greedy' ? data[row].join('').trim() === '' : data[row].length === 1 && data[row][0].length === 0;\n\t\t\t\t}\n\t\t\t\tif (skipEmptyLines === 'greedy' && hasHeader) {\n\t\t\t\t\tvar line = [];\n\t\t\t\t\tfor (var c = 0; c < maxCol; c++) {\n\t\t\t\t\t\tvar cx = dataKeyedByField ? fields[c] : c;\n\t\t\t\t\t\tline.push(data[row][cx]);\n\t\t\t\t\t}\n\t\t\t\t\temptyLine = line.join('').trim() === '';\n\t\t\t\t}\n\t\t\t\tif (!emptyLine)\n\t\t\t\t{\n\t\t\t\t\tfor (var col = 0; col < maxCol; col++)\n\t\t\t\t\t{\n\t\t\t\t\t\tif (col > 0 && !nullLine)\n\t\t\t\t\t\t\tcsv += _delimiter;\n\t\t\t\t\t\tvar colIdx = hasHeader && dataKeyedByField ? fields[col] : col;\n\t\t\t\t\t\tcsv += safe(data[row][colIdx], col);\n\t\t\t\t\t}\n\t\t\t\t\tif (row < data.length - 1 && (!skipEmptyLines || (maxCol > 0 && !nullLine)))\n\t\t\t\t\t{\n\t\t\t\t\t\tcsv += _newline;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn csv;\n\t\t}\n\n\t\t/** Encloses a value around quotes if needed (makes a value safe for CSV insertion) */\n\t\tfunction safe(str, col)\n\t\t{\n\t\t\tif (typeof str === 'undefined' || str === null)\n\t\t\t\treturn '';\n\n\t\t\tif (str.constructor === Date)\n\t\t\t\treturn JSON.stringify(str).slice(1, 25);\n\n\t\t\tvar needsQuotes = false;\n\n\t\t\tif (_escapeFormulae && typeof str === \"string\" && _escapeFormulae.test(str)) {\n\t\t\t\tstr = \"'\" + str;\n\t\t\t\tneedsQuotes = true;\n\t\t\t}\n\n\t\t\tvar escapedQuoteStr = str.toString().replace(quoteCharRegex, _escapedQuote);\n\n\t\t\tneedsQuotes = needsQuotes\n\t\t\t\t\t\t\t|| _quotes === true\n\t\t\t\t\t\t\t|| (typeof _quotes === 'function' && _quotes(str, col))\n\t\t\t\t\t\t\t|| (Array.isArray(_quotes) && _quotes[col])\n\t\t\t\t\t\t\t|| hasAny(escapedQuoteStr, Papa.BAD_DELIMITERS)\n\t\t\t\t\t\t\t|| escapedQuoteStr.indexOf(_delimiter) > -1\n\t\t\t\t\t\t\t|| escapedQuoteStr.charAt(0) === ' '\n\t\t\t\t\t\t\t|| escapedQuoteStr.charAt(escapedQuoteStr.length - 1) === ' ';\n\n\t\t\treturn needsQuotes ? _quoteChar + escapedQuoteStr + _quoteChar : escapedQuoteStr;\n\t\t}\n\n\t\tfunction hasAny(str, substrings)\n\t\t{\n\t\t\tfor (var i = 0; i < substrings.length; i++)\n\t\t\t\tif (str.indexOf(substrings[i]) > -1)\n\t\t\t\t\treturn true;\n\t\t\treturn false;\n\t\t}\n\t}\n\n\n\t/** ChunkStreamer is the base prototype for various streamer implementations. */\n\tfunction ChunkStreamer(config)\n\t{\n\t\tthis._handle = null;\n\t\tthis._finished = false;\n\t\tthis._completed = false;\n\t\tthis._halted = false;\n\t\tthis._input = null;\n\t\tthis._baseIndex = 0;\n\t\tthis._partialLine = '';\n\t\tthis._rowCount = 0;\n\t\tthis._start = 0;\n\t\tthis._nextChunk = null;\n\t\tthis.isFirstChunk = true;\n\t\tthis._completeResults = {\n\t\t\tdata: [],\n\t\t\terrors: [],\n\t\t\tmeta: {}\n\t\t};\n\t\treplaceConfig.call(this, config);\n\n\t\tthis.parseChunk = function(chunk, isFakeChunk)\n\t\t{\n\t\t\t// First chunk pre-processing\n\t\t\tconst skipFirstNLines = parseInt(this._config.skipFirstNLines) || 0;\n\t\t\tif (this.isFirstChunk && skipFirstNLines > 0) {\n\t\t\t\tlet _newline = this._config.newline;\n\t\t\t\tif (!_newline) {\n\t\t\t\t\tconst quoteChar = this._config.quoteChar || '\"';\n\t\t\t\t\t_newline = this._handle.guessLineEndings(chunk, quoteChar);\n\t\t\t\t}\n\t\t\t\tconst splitChunk = chunk.split(_newline);\n\t\t\t\tchunk = [...splitChunk.slice(skipFirstNLines)].join(_newline);\n\t\t\t}\n\t\t\tif (this.isFirstChunk && isFunction(this._config.beforeFirstChunk))\n\t\t\t{\n\t\t\t\tvar modifiedChunk = this._config.beforeFirstChunk(chunk);\n\t\t\t\tif (modifiedChunk !== undefined)\n\t\t\t\t\tchunk = modifiedChunk;\n\t\t\t}\n\t\t\tthis.isFirstChunk = false;\n\t\t\tthis._halted = false;\n\n\t\t\t// Rejoin the line we likely just split in two by chunking the file\n\t\t\tvar aggregate = this._partialLine + chunk;\n\t\t\tthis._partialLine = '';\n\t\t\tvar results = this._handle.parse(aggregate, this._baseIndex, !this._finished);\n\n\t\t\tif (this._handle.paused() || this._handle.aborted()) {\n\t\t\t\tthis._halted = true;\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\tvar lastIndex = results.meta.cursor;\n\n\t\t\tif (!this._finished)\n\t\t\t{\n\t\t\t\tthis._partialLine = aggregate.substring(lastIndex - this._baseIndex);\n\t\t\t\tthis._baseIndex = lastIndex;\n\t\t\t}\n\n\t\t\tif (results && results.data)\n\t\t\t\tthis._rowCount += results.data.length;\n\n\t\t\tvar finishedIncludingPreview = this._finished || (this._config.preview && this._rowCount >= this._config.preview);\n\n\t\t\tif (IS_PAPA_WORKER)\n\t\t\t{\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tresults: results,\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\tfinished: finishedIncludingPreview\n\t\t\t\t});\n\t\t\t}\n\t\t\telse if (isFunction(this._config.chunk) && !isFakeChunk)\n\t\t\t{\n\t\t\t\tthis._config.chunk(results, this._handle);\n\t\t\t\tif (this._handle.paused() || this._handle.aborted()) {\n\t\t\t\t\tthis._halted = true;\n\t\t\t\t\treturn;\n\t\t\t\t}\n\t\t\t\tresults = undefined;\n\t\t\t\tthis._completeResults = undefined;\n\t\t\t}\n\n\t\t\tif (!this._config.step && !this._config.chunk) {\n\t\t\t\tthis._completeResults.data = this._completeResults.data.concat(results.data);\n\t\t\t\tthis._completeResults.errors = this._completeResults.errors.concat(results.errors);\n\t\t\t\tthis._completeResults.meta = results.meta;\n\t\t\t}\n\n\t\t\tif (!this._completed && finishedIncludingPreview && isFunction(this._config.complete) && (!results || !results.meta.aborted)) {\n\t\t\t\tthis._config.complete(this._completeResults, this._input);\n\t\t\t\tthis._completed = true;\n\t\t\t}\n\n\t\t\tif (!finishedIncludingPreview && (!results || !results.meta.paused))\n\t\t\t\tthis._nextChunk();\n\n\t\t\treturn results;\n\t\t};\n\n\t\tthis._sendError = function(error)\n\t\t{\n\t\t\tif (isFunction(this._config.error))\n\t\t\t\tthis._config.error(error);\n\t\t\telse if (IS_PAPA_WORKER && this._config.error)\n\t\t\t{\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\terror: error,\n\t\t\t\t\tfinished: false\n\t\t\t\t});\n\t\t\t}\n\t\t};\n\n\t\tfunction replaceConfig(config)\n\t\t{\n\t\t\t// Deep-copy the config so we can edit it\n\t\t\tvar configCopy = copy(config);\n\t\t\tconfigCopy.chunkSize = parseInt(configCopy.chunkSize);\t// parseInt VERY important so we don't concatenate strings!\n\t\t\tif (!config.step && !config.chunk)\n\t\t\t\tconfigCopy.chunkSize = null;  // disable Range header if not streaming; bad values break IIS - see issue #196\n\t\t\tthis._handle = new ParserHandle(configCopy);\n\t\t\tthis._handle.streamer = this;\n\t\t\tthis._config = configCopy;\t// persist the copy to the caller\n\t\t}\n\t}\n\n\n\tfunction NetworkStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tif (!config.chunkSize)\n\t\t\tconfig.chunkSize = Papa.RemoteChunkSize;\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar xhr;\n\n\t\tif (IS_WORKER)\n\t\t{\n\t\t\tthis._nextChunk = function()\n\t\t\t{\n\t\t\t\tthis._readChunk();\n\t\t\t\tthis._chunkLoaded();\n\t\t\t};\n\t\t}\n\t\telse\n\t\t{\n\t\t\tthis._nextChunk = function()\n\t\t\t{\n\t\t\t\tthis._readChunk();\n\t\t\t};\n\t\t}\n\n\t\tthis.stream = function(url)\n\t\t{\n\t\t\tthis._input = url;\n\t\t\tthis._nextChunk();\t// Starts streaming\n\t\t};\n\n\t\tthis._readChunk = function()\n\t\t{\n\t\t\tif (this._finished)\n\t\t\t{\n\t\t\t\tthis._chunkLoaded();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\txhr = new XMLHttpRequest();\n\n\t\t\tif (this._config.withCredentials)\n\t\t\t{\n\t\t\t\txhr.withCredentials = this._config.withCredentials;\n\t\t\t}\n\n\t\t\tif (!IS_WORKER)\n\t\t\t{\n\t\t\t\txhr.onload = bindFunction(this._chunkLoaded, this);\n\t\t\t\txhr.onerror = bindFunction(this._chunkError, this);\n\t\t\t}\n\n\t\t\txhr.open(this._config.downloadRequestBody ? 'POST' : 'GET', this._input, !IS_WORKER);\n\t\t\t// Headers can only be set when once the request state is OPENED\n\t\t\tif (this._config.downloadRequestHeaders)\n\t\t\t{\n\t\t\t\tvar headers = this._config.downloadRequestHeaders;\n\n\t\t\t\tfor (var headerName in headers)\n\t\t\t\t{\n\t\t\t\t\txhr.setRequestHeader(headerName, headers[headerName]);\n\t\t\t\t}\n\t\t\t}\n\n\t\t\tif (this._config.chunkSize)\n\t\t\t{\n\t\t\t\tvar end = this._start + this._config.chunkSize - 1;\t// minus one because byte range is inclusive\n\t\t\t\txhr.setRequestHeader('Range', 'bytes=' + this._start + '-' + end);\n\t\t\t}\n\n\t\t\ttry {\n\t\t\t\txhr.send(this._config.downloadRequestBody);\n\t\t\t}\n\t\t\tcatch (err) {\n\t\t\t\tthis._chunkError(err.message);\n\t\t\t}\n\n\t\t\tif (IS_WORKER && xhr.status === 0)\n\t\t\t\tthis._chunkError();\n\t\t};\n\n\t\tthis._chunkLoaded = function()\n\t\t{\n\t\t\tif (xhr.readyState !== 4)\n\t\t\t\treturn;\n\n\t\t\tif (xhr.status < 200 || xhr.status >= 400)\n\t\t\t{\n\t\t\t\tthis._chunkError();\n\t\t\t\treturn;\n\t\t\t}\n\n\t\t\t// Use chunckSize as it may be a diference on reponse lentgh due to characters with more than 1 byte\n\t\t\tthis._start += this._config.chunkSize ? this._config.chunkSize : xhr.responseText.length;\n\t\t\tthis._finished = !this._config.chunkSize || this._start >= getFileSize(xhr);\n\t\t\tthis.parseChunk(xhr.responseText);\n\t\t};\n\n\t\tthis._chunkError = function(errorMessage)\n\t\t{\n\t\t\tvar errorText = xhr.statusText || errorMessage;\n\t\t\tthis._sendError(new Error(errorText));\n\t\t};\n\n\t\tfunction getFileSize(xhr)\n\t\t{\n\t\t\tvar contentRange = xhr.getResponseHeader('Content-Range');\n\t\t\tif (contentRange === null) { // no content range, then finish!\n\t\t\t\treturn -1;\n\t\t\t}\n\t\t\treturn parseInt(contentRange.substring(contentRange.lastIndexOf('/') + 1));\n\t\t}\n\t}\n\tNetworkStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tNetworkStreamer.prototype.constructor = NetworkStreamer;\n\n\n\tfunction FileStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tif (!config.chunkSize)\n\t\t\tconfig.chunkSize = Papa.LocalChunkSize;\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar reader, slice;\n\n\t\t// FileReader is better than FileReaderSync (even in worker) - see http://stackoverflow.com/q/24708649/1048862\n\t\t// But Firefox is a pill, too - see issue #76: https://github.com/mholt/PapaParse/issues/76\n\t\tvar usingAsyncReader = typeof FileReader !== 'undefined';\t// Safari doesn't consider it a function - see issue #105\n\n\t\tthis.stream = function(file)\n\t\t{\n\t\t\tthis._input = file;\n\t\t\tslice = file.slice || file.webkitSlice || file.mozSlice;\n\n\t\t\tif (usingAsyncReader)\n\t\t\t{\n\t\t\t\treader = new FileReader();\t\t// Preferred method of reading files, even in workers\n\t\t\t\treader.onload = bindFunction(this._chunkLoaded, this);\n\t\t\t\treader.onerror = bindFunction(this._chunkError, this);\n\t\t\t}\n\t\t\telse\n\t\t\t\treader = new FileReaderSync();\t// Hack for running in a web worker in Firefox\n\n\t\t\tthis._nextChunk();\t// Starts streaming\n\t\t};\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (!this._finished && (!this._config.preview || this._rowCount < this._config.preview))\n\t\t\t\tthis._readChunk();\n\t\t};\n\n\t\tthis._readChunk = function()\n\t\t{\n\t\t\tvar input = this._input;\n\t\t\tif (this._config.chunkSize)\n\t\t\t{\n\t\t\t\tvar end = Math.min(this._start + this._config.chunkSize, this._input.size);\n\t\t\t\tinput = slice.call(input, this._start, end);\n\t\t\t}\n\t\t\tvar txt = reader.readAsText(input, this._config.encoding);\n\t\t\tif (!usingAsyncReader)\n\t\t\t\tthis._chunkLoaded({ target: { result: txt } });\t// mimic the async signature\n\t\t};\n\n\t\tthis._chunkLoaded = function(event)\n\t\t{\n\t\t\t// Very important to increment start each time before handling results\n\t\t\tthis._start += this._config.chunkSize;\n\t\t\tthis._finished = !this._config.chunkSize || this._start >= this._input.size;\n\t\t\tthis.parseChunk(event.target.result);\n\t\t};\n\n\t\tthis._chunkError = function()\n\t\t{\n\t\t\tthis._sendError(reader.error);\n\t\t};\n\n\t}\n\tFileStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tFileStreamer.prototype.constructor = FileStreamer;\n\n\n\tfunction StringStreamer(config)\n\t{\n\t\tconfig = config || {};\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar remaining;\n\t\tthis.stream = function(s)\n\t\t{\n\t\t\tremaining = s;\n\t\t\treturn this._nextChunk();\n\t\t};\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (this._finished) return;\n\t\t\tvar size = this._config.chunkSize;\n\t\t\tvar chunk;\n\t\t\tif(size) {\n\t\t\t\tchunk = remaining.substring(0, size);\n\t\t\t\tremaining = remaining.substring(size);\n\t\t\t} else {\n\t\t\t\tchunk = remaining;\n\t\t\t\tremaining = '';\n\t\t\t}\n\t\t\tthis._finished = !remaining;\n\t\t\treturn this.parseChunk(chunk);\n\t\t};\n\t}\n\tStringStreamer.prototype = Object.create(StringStreamer.prototype);\n\tStringStreamer.prototype.constructor = StringStreamer;\n\n\n\tfunction ReadableStreamStreamer(config)\n\t{\n\t\tconfig = config || {};\n\n\t\tChunkStreamer.call(this, config);\n\n\t\tvar queue = [];\n\t\tvar parseOnData = true;\n\t\tvar streamHasEnded = false;\n\n\t\tthis.pause = function()\n\t\t{\n\t\t\tChunkStreamer.prototype.pause.apply(this, arguments);\n\t\t\tthis._input.pause();\n\t\t};\n\n\t\tthis.resume = function()\n\t\t{\n\t\t\tChunkStreamer.prototype.resume.apply(this, arguments);\n\t\t\tthis._input.resume();\n\t\t};\n\n\t\tthis.stream = function(stream)\n\t\t{\n\t\t\tthis._input = stream;\n\n\t\t\tthis._input.on('data', this._streamData);\n\t\t\tthis._input.on('end', this._streamEnd);\n\t\t\tthis._input.on('error', this._streamError);\n\t\t};\n\n\t\tthis._checkIsFinished = function()\n\t\t{\n\t\t\tif (streamHasEnded && queue.length === 1) {\n\t\t\t\tthis._finished = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tthis._checkIsFinished();\n\t\t\tif (queue.length)\n\t\t\t{\n\t\t\t\tthis.parseChunk(queue.shift());\n\t\t\t}\n\t\t\telse\n\t\t\t{\n\t\t\t\tparseOnData = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._streamData = bindFunction(function(chunk)\n\t\t{\n\t\t\ttry\n\t\t\t{\n\t\t\t\tqueue.push(typeof chunk === 'string' ? chunk : chunk.toString(this._config.encoding));\n\n\t\t\t\tif (parseOnData)\n\t\t\t\t{\n\t\t\t\t\tparseOnData = false;\n\t\t\t\t\tthis._checkIsFinished();\n\t\t\t\t\tthis.parseChunk(queue.shift());\n\t\t\t\t}\n\t\t\t}\n\t\t\tcatch (error)\n\t\t\t{\n\t\t\t\tthis._streamError(error);\n\t\t\t}\n\t\t}, this);\n\n\t\tthis._streamError = bindFunction(function(error)\n\t\t{\n\t\t\tthis._streamCleanUp();\n\t\t\tthis._sendError(error);\n\t\t}, this);\n\n\t\tthis._streamEnd = bindFunction(function()\n\t\t{\n\t\t\tthis._streamCleanUp();\n\t\t\tstreamHasEnded = true;\n\t\t\tthis._streamData('');\n\t\t}, this);\n\n\t\tthis._streamCleanUp = bindFunction(function()\n\t\t{\n\t\t\tthis._input.removeListener('data', this._streamData);\n\t\t\tthis._input.removeListener('end', this._streamEnd);\n\t\t\tthis._input.removeListener('error', this._streamError);\n\t\t}, this);\n\t}\n\tReadableStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\tReadableStreamStreamer.prototype.constructor = ReadableStreamStreamer;\n\n\n\tfunction DuplexStreamStreamer(_config) {\n\t\tvar Duplex = (__webpack_require__(/*! stream */ \"stream\").Duplex);\n\t\tvar config = copy(_config);\n\t\tvar parseOnWrite = true;\n\t\tvar writeStreamHasFinished = false;\n\t\tvar parseCallbackQueue = [];\n\t\tvar stream = null;\n\n\t\tthis._onCsvData = function(results)\n\t\t{\n\t\t\tvar data = results.data;\n\t\t\tif (!stream.push(data) && !this._handle.paused()) {\n\t\t\t\t// the writeable consumer buffer has filled up\n\t\t\t\t// so we need to pause until more items\n\t\t\t\t// can be processed\n\t\t\t\tthis._handle.pause();\n\t\t\t}\n\t\t};\n\n\t\tthis._onCsvComplete = function()\n\t\t{\n\t\t\t// node will finish the read stream when\n\t\t\t// null is pushed\n\t\t\tstream.push(null);\n\t\t};\n\n\t\tconfig.step = bindFunction(this._onCsvData, this);\n\t\tconfig.complete = bindFunction(this._onCsvComplete, this);\n\t\tChunkStreamer.call(this, config);\n\n\t\tthis._nextChunk = function()\n\t\t{\n\t\t\tif (writeStreamHasFinished && parseCallbackQueue.length === 1) {\n\t\t\t\tthis._finished = true;\n\t\t\t}\n\t\t\tif (parseCallbackQueue.length) {\n\t\t\t\tparseCallbackQueue.shift()();\n\t\t\t} else {\n\t\t\t\tparseOnWrite = true;\n\t\t\t}\n\t\t};\n\n\t\tthis._addToParseQueue = function(chunk, callback)\n\t\t{\n\t\t\t// add to queue so that we can indicate\n\t\t\t// completion via callback\n\t\t\t// node will automatically pause the incoming stream\n\t\t\t// when too many items have been added without their\n\t\t\t// callback being invoked\n\t\t\tparseCallbackQueue.push(bindFunction(function() {\n\t\t\t\tthis.parseChunk(typeof chunk === 'string' ? chunk : chunk.toString(config.encoding));\n\t\t\t\tif (isFunction(callback)) {\n\t\t\t\t\treturn callback();\n\t\t\t\t}\n\t\t\t}, this));\n\t\t\tif (parseOnWrite) {\n\t\t\t\tparseOnWrite = false;\n\t\t\t\tthis._nextChunk();\n\t\t\t}\n\t\t};\n\n\t\tthis._onRead = function()\n\t\t{\n\t\t\tif (this._handle.paused()) {\n\t\t\t\t// the writeable consumer can handle more data\n\t\t\t\t// so resume the chunk parsing\n\t\t\t\tthis._handle.resume();\n\t\t\t}\n\t\t};\n\n\t\tthis._onWrite = function(chunk, encoding, callback)\n\t\t{\n\t\t\tthis._addToParseQueue(chunk, callback);\n\t\t};\n\n\t\tthis._onWriteComplete = function()\n\t\t{\n\t\t\twriteStreamHasFinished = true;\n\t\t\t// have to write empty string\n\t\t\t// so parser knows its done\n\t\t\tthis._addToParseQueue('');\n\t\t};\n\n\t\tthis.getStream = function()\n\t\t{\n\t\t\treturn stream;\n\t\t};\n\t\tstream = new Duplex({\n\t\t\treadableObjectMode: true,\n\t\t\tdecodeStrings: false,\n\t\t\tread: bindFunction(this._onRead, this),\n\t\t\twrite: bindFunction(this._onWrite, this)\n\t\t});\n\t\tstream.once('finish', bindFunction(this._onWriteComplete, this));\n\t}\n\tif (typeof PAPA_BROWSER_CONTEXT === 'undefined') {\n\t\tDuplexStreamStreamer.prototype = Object.create(ChunkStreamer.prototype);\n\t\tDuplexStreamStreamer.prototype.constructor = DuplexStreamStreamer;\n\t}\n\n\n\t// Use one ParserHandle per entire CSV file or string\n\tfunction ParserHandle(_config)\n\t{\n\t\t// One goal is to minimize the use of regular expressions...\n\t\tvar MAX_FLOAT = Math.pow(2, 53);\n\t\tvar MIN_FLOAT = -MAX_FLOAT;\n\t\tvar FLOAT = /^\\s*-?(\\d+\\.?|\\.\\d+|\\d+\\.\\d+)([eE][-+]?\\d+)?\\s*$/;\n\t\tvar ISO_DATE = /^((\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d\\.\\d+([+-][0-2]\\d:[0-5]\\d|Z))|(\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z))|(\\d{4}-[01]\\d-[0-3]\\dT[0-2]\\d:[0-5]\\d([+-][0-2]\\d:[0-5]\\d|Z)))$/;\n\t\tvar self = this;\n\t\tvar _stepCounter = 0;\t// Number of times step was called (number of rows parsed)\n\t\tvar _rowCounter = 0;\t// Number of rows that have been parsed so far\n\t\tvar _input;\t\t\t\t// The input being parsed\n\t\tvar _parser;\t\t\t// The core parser being used\n\t\tvar _paused = false;\t// Whether we are paused or not\n\t\tvar _aborted = false;\t// Whether the parser has aborted or not\n\t\tvar _delimiterError;\t// Temporary state between delimiter detection and processing results\n\t\tvar _fields = [];\t\t// Fields are from the header row of the input, if there is one\n\t\tvar _results = {\t\t// The last results returned from the parser\n\t\t\tdata: [],\n\t\t\terrors: [],\n\t\t\tmeta: {}\n\t\t};\n\n\t\tif (isFunction(_config.step))\n\t\t{\n\t\t\tvar userStep = _config.step;\n\t\t\t_config.step = function(results)\n\t\t\t{\n\t\t\t\t_results = results;\n\n\t\t\t\tif (needsHeaderRow())\n\t\t\t\t\tprocessResults();\n\t\t\t\telse\t// only call user's step function after header row\n\t\t\t\t{\n\t\t\t\t\tprocessResults();\n\n\t\t\t\t\t// It's possbile that this line was empty and there's no row here after all\n\t\t\t\t\tif (_results.data.length === 0)\n\t\t\t\t\t\treturn;\n\n\t\t\t\t\t_stepCounter += results.data.length;\n\t\t\t\t\tif (_config.preview && _stepCounter > _config.preview)\n\t\t\t\t\t\t_parser.abort();\n\t\t\t\t\telse {\n\t\t\t\t\t\t_results.data = _results.data[0];\n\t\t\t\t\t\tuserStep(_results, self);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t};\n\t\t}\n\n\t\t/**\n\t\t * Parses input. Most users won't need, and shouldn't mess with, the baseIndex\n\t\t * and ignoreLastRow parameters. They are used by streamers (wrapper functions)\n\t\t * when an input comes in multiple chunks, like from a file.\n\t\t */\n\t\tthis.parse = function(input, baseIndex, ignoreLastRow)\n\t\t{\n\t\t\tvar quoteChar = _config.quoteChar || '\"';\n\t\t\tif (!_config.newline)\n\t\t\t\t_config.newline = this.guessLineEndings(input, quoteChar);\n\n\t\t\t_delimiterError = false;\n\t\t\tif (!_config.delimiter)\n\t\t\t{\n\t\t\t\tvar delimGuess = guessDelimiter(input, _config.newline, _config.skipEmptyLines, _config.comments, _config.delimitersToGuess);\n\t\t\t\tif (delimGuess.successful)\n\t\t\t\t\t_config.delimiter = delimGuess.bestDelimiter;\n\t\t\t\telse\n\t\t\t\t{\n\t\t\t\t\t_delimiterError = true;\t// add error after parsing (otherwise it would be overwritten)\n\t\t\t\t\t_config.delimiter = Papa.DefaultDelimiter;\n\t\t\t\t}\n\t\t\t\t_results.meta.delimiter = _config.delimiter;\n\t\t\t}\n\t\t\telse if(isFunction(_config.delimiter))\n\t\t\t{\n\t\t\t\t_config.delimiter = _config.delimiter(input);\n\t\t\t\t_results.meta.delimiter = _config.delimiter;\n\t\t\t}\n\n\t\t\tvar parserConfig = copy(_config);\n\t\t\tif (_config.preview && _config.header)\n\t\t\t\tparserConfig.preview++;\t// to compensate for header row\n\n\t\t\t_input = input;\n\t\t\t_parser = new Parser(parserConfig);\n\t\t\t_results = _parser.parse(_input, baseIndex, ignoreLastRow);\n\t\t\tprocessResults();\n\t\t\treturn _paused ? { meta: { paused: true } } : (_results || { meta: { paused: false } });\n\t\t};\n\n\t\tthis.paused = function()\n\t\t{\n\t\t\treturn _paused;\n\t\t};\n\n\t\tthis.pause = function()\n\t\t{\n\t\t\t_paused = true;\n\t\t\t_parser.abort();\n\n\t\t\t// If it is streaming via \"chunking\", the reader will start appending correctly already so no need to substring,\n\t\t\t// otherwise we can get duplicate content within a row\n\t\t\t_input = isFunction(_config.chunk) ? \"\" : _input.substring(_parser.getCharIndex());\n\t\t};\n\n\t\tthis.resume = function()\n\t\t{\n\t\t\tif(self.streamer._halted) {\n\t\t\t\t_paused = false;\n\t\t\t\tself.streamer.parseChunk(_input, true);\n\t\t\t} else {\n\t\t\t\t// Bugfix: #636 In case the processing hasn't halted yet\n\t\t\t\t// wait for it to halt in order to resume\n\t\t\t\tsetTimeout(self.resume, 3);\n\t\t\t}\n\t\t};\n\n\t\tthis.aborted = function()\n\t\t{\n\t\t\treturn _aborted;\n\t\t};\n\n\t\tthis.abort = function()\n\t\t{\n\t\t\t_aborted = true;\n\t\t\t_parser.abort();\n\t\t\t_results.meta.aborted = true;\n\t\t\tif (isFunction(_config.complete))\n\t\t\t\t_config.complete(_results);\n\t\t\t_input = '';\n\t\t};\n\n\t\tthis.guessLineEndings = function(input, quoteChar)\n\t\t{\n\t\t\tinput = input.substring(0, 1024 * 1024);\t// max length 1 MB\n\t\t\t// Replace all the text inside quotes\n\t\t\tvar re = new RegExp(escapeRegExp(quoteChar) + '([^]*?)' + escapeRegExp(quoteChar), 'gm');\n\t\t\tinput = input.replace(re, '');\n\n\t\t\tvar r = input.split('\\r');\n\n\t\t\tvar n = input.split('\\n');\n\n\t\t\tvar nAppearsFirst = (n.length > 1 && n[0].length < r[0].length);\n\n\t\t\tif (r.length === 1 || nAppearsFirst)\n\t\t\t\treturn '\\n';\n\n\t\t\tvar numWithN = 0;\n\t\t\tfor (var i = 0; i < r.length; i++)\n\t\t\t{\n\t\t\t\tif (r[i][0] === '\\n')\n\t\t\t\t\tnumWithN++;\n\t\t\t}\n\n\t\t\treturn numWithN >= r.length / 2 ? '\\r\\n' : '\\r';\n\t\t};\n\n\t\tfunction testEmptyLine(s) {\n\t\t\treturn _config.skipEmptyLines === 'greedy' ? s.join('').trim() === '' : s.length === 1 && s[0].length === 0;\n\t\t}\n\n\t\tfunction testFloat(s) {\n\t\t\tif (FLOAT.test(s)) {\n\t\t\t\tvar floatValue = parseFloat(s);\n\t\t\t\tif (floatValue > MIN_FLOAT && floatValue < MAX_FLOAT) {\n\t\t\t\t\treturn true;\n\t\t\t\t}\n\t\t\t}\n\t\t\treturn false;\n\t\t}\n\n\t\tfunction processResults()\n\t\t{\n\t\t\tif (_results && _delimiterError)\n\t\t\t{\n\t\t\t\taddError('Delimiter', 'UndetectableDelimiter', 'Unable to auto-detect delimiting character; defaulted to \\'' + Papa.DefaultDelimiter + '\\'');\n\t\t\t\t_delimiterError = false;\n\t\t\t}\n\n\t\t\tif (_config.skipEmptyLines)\n\t\t\t{\n\t\t\t\t_results.data = _results.data.filter(function(d) {\n\t\t\t\t\treturn !testEmptyLine(d);\n\t\t\t\t});\n\t\t\t}\n\n\t\t\tif (needsHeaderRow())\n\t\t\t\tfillHeaderFields();\n\n\t\t\treturn applyHeaderAndDynamicTypingAndTransformation();\n\t\t}\n\n\t\tfunction needsHeaderRow()\n\t\t{\n\t\t\treturn _config.header && _fields.length === 0;\n\t\t}\n\n\t\tfunction fillHeaderFields()\n\t\t{\n\t\t\tif (!_results)\n\t\t\t\treturn;\n\n\t\t\tfunction addHeader(header, i)\n\t\t\t{\n\t\t\t\tif (isFunction(_config.transformHeader))\n\t\t\t\t\theader = _config.transformHeader(header, i);\n\n\t\t\t\t_fields.push(header);\n\t\t\t}\n\n\t\t\tif (Array.isArray(_results.data[0]))\n\t\t\t{\n\t\t\t\tfor (var i = 0; needsHeaderRow() && i < _results.data.length; i++)\n\t\t\t\t\t_results.data[i].forEach(addHeader);\n\n\t\t\t\t_results.data.splice(0, 1);\n\t\t\t}\n\t\t\t// if _results.data[0] is not an array, we are in a step where _results.data is the row.\n\t\t\telse\n\t\t\t\t_results.data.forEach(addHeader);\n\t\t}\n\n\t\tfunction shouldApplyDynamicTyping(field) {\n\t\t\t// Cache function values to avoid calling it for each row\n\t\t\tif (_config.dynamicTypingFunction && _config.dynamicTyping[field] === undefined) {\n\t\t\t\t_config.dynamicTyping[field] = _config.dynamicTypingFunction(field);\n\t\t\t}\n\t\t\treturn (_config.dynamicTyping[field] || _config.dynamicTyping) === true;\n\t\t}\n\n\t\tfunction parseDynamic(field, value)\n\t\t{\n\t\t\tif (shouldApplyDynamicTyping(field))\n\t\t\t{\n\t\t\t\tif (value === 'true' || value === 'TRUE')\n\t\t\t\t\treturn true;\n\t\t\t\telse if (value === 'false' || value === 'FALSE')\n\t\t\t\t\treturn false;\n\t\t\t\telse if (testFloat(value))\n\t\t\t\t\treturn parseFloat(value);\n\t\t\t\telse if (ISO_DATE.test(value))\n\t\t\t\t\treturn new Date(value);\n\t\t\t\telse\n\t\t\t\t\treturn (value === '' ? null : value);\n\t\t\t}\n\t\t\treturn value;\n\t\t}\n\n\t\tfunction applyHeaderAndDynamicTypingAndTransformation()\n\t\t{\n\t\t\tif (!_results || (!_config.header && !_config.dynamicTyping && !_config.transform))\n\t\t\t\treturn _results;\n\n\t\t\tfunction processRow(rowSource, i)\n\t\t\t{\n\t\t\t\tvar row = _config.header ? {} : [];\n\n\t\t\t\tvar j;\n\t\t\t\tfor (j = 0; j < rowSource.length; j++)\n\t\t\t\t{\n\t\t\t\t\tvar field = j;\n\t\t\t\t\tvar value = rowSource[j];\n\n\t\t\t\t\tif (_config.header)\n\t\t\t\t\t\tfield = j >= _fields.length ? '__parsed_extra' : _fields[j];\n\n\t\t\t\t\tif (_config.transform)\n\t\t\t\t\t\tvalue = _config.transform(value,field);\n\n\t\t\t\t\tvalue = parseDynamic(field, value);\n\n\t\t\t\t\tif (field === '__parsed_extra')\n\t\t\t\t\t{\n\t\t\t\t\t\trow[field] = row[field] || [];\n\t\t\t\t\t\trow[field].push(value);\n\t\t\t\t\t}\n\t\t\t\t\telse\n\t\t\t\t\t\trow[field] = value;\n\t\t\t\t}\n\n\n\t\t\t\tif (_config.header)\n\t\t\t\t{\n\t\t\t\t\tif (j > _fields.length)\n\t\t\t\t\t\taddError('FieldMismatch', 'TooManyFields', 'Too many fields: expected ' + _fields.length + ' fields but parsed ' + j, _rowCounter + i);\n\t\t\t\t\telse if (j < _fields.length)\n\t\t\t\t\t\taddError('FieldMismatch', 'TooFewFields', 'Too few fields: expected ' + _fields.length + ' fields but parsed ' + j, _rowCounter + i);\n\t\t\t\t}\n\n\t\t\t\treturn row;\n\t\t\t}\n\n\t\t\tvar incrementBy = 1;\n\t\t\tif (!_results.data.length || Array.isArray(_results.data[0]))\n\t\t\t{\n\t\t\t\t_results.data = _results.data.map(processRow);\n\t\t\t\tincrementBy = _results.data.length;\n\t\t\t}\n\t\t\telse\n\t\t\t\t_results.data = processRow(_results.data, 0);\n\n\n\t\t\tif (_config.header && _results.meta)\n\t\t\t\t_results.meta.fields = _fields;\n\n\t\t\t_rowCounter += incrementBy;\n\t\t\treturn _results;\n\t\t}\n\n\t\tfunction guessDelimiter(input, newline, skipEmptyLines, comments, delimitersToGuess) {\n\t\t\tvar bestDelim, bestDelta, fieldCountPrevRow, maxFieldCount;\n\n\t\t\tdelimitersToGuess = delimitersToGuess || [',', '\\t', '|', ';', Papa.RECORD_SEP, Papa.UNIT_SEP];\n\n\t\t\tfor (var i = 0; i < delimitersToGuess.length; i++) {\n\t\t\t\tvar delim = delimitersToGuess[i];\n\t\t\t\tvar delta = 0, avgFieldCount = 0, emptyLinesCount = 0;\n\t\t\t\tfieldCountPrevRow = undefined;\n\n\t\t\t\tvar preview = new Parser({\n\t\t\t\t\tcomments: comments,\n\t\t\t\t\tdelimiter: delim,\n\t\t\t\t\tnewline: newline,\n\t\t\t\t\tpreview: 10\n\t\t\t\t}).parse(input);\n\n\t\t\t\tfor (var j = 0; j < preview.data.length; j++) {\n\t\t\t\t\tif (skipEmptyLines && testEmptyLine(preview.data[j])) {\n\t\t\t\t\t\temptyLinesCount++;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\tvar fieldCount = preview.data[j].length;\n\t\t\t\t\tavgFieldCount += fieldCount;\n\n\t\t\t\t\tif (typeof fieldCountPrevRow === 'undefined') {\n\t\t\t\t\t\tfieldCountPrevRow = fieldCount;\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\t}\n\t\t\t\t\telse if (fieldCount > 0) {\n\t\t\t\t\t\tdelta += Math.abs(fieldCount - fieldCountPrevRow);\n\t\t\t\t\t\tfieldCountPrevRow = fieldCount;\n\t\t\t\t\t}\n\t\t\t\t}\n\n\t\t\t\tif (preview.data.length > 0)\n\t\t\t\t\tavgFieldCount /= (preview.data.length - emptyLinesCount);\n\n\t\t\t\tif ((typeof bestDelta === 'undefined' || delta <= bestDelta)\n\t\t\t\t\t&& (typeof maxFieldCount === 'undefined' || avgFieldCount > maxFieldCount) && avgFieldCount > 1.99) {\n\t\t\t\t\tbestDelta = delta;\n\t\t\t\t\tbestDelim = delim;\n\t\t\t\t\tmaxFieldCount = avgFieldCount;\n\t\t\t\t}\n\t\t\t}\n\n\t\t\t_config.delimiter = bestDelim;\n\n\t\t\treturn {\n\t\t\t\tsuccessful: !!bestDelim,\n\t\t\t\tbestDelimiter: bestDelim\n\t\t\t};\n\t\t}\n\n\t\tfunction addError(type, code, msg, row)\n\t\t{\n\t\t\tvar error = {\n\t\t\t\ttype: type,\n\t\t\t\tcode: code,\n\t\t\t\tmessage: msg\n\t\t\t};\n\t\t\tif(row !== undefined) {\n\t\t\t\terror.row = row;\n\t\t\t}\n\t\t\t_results.errors.push(error);\n\t\t}\n\t}\n\n\t/** https://developer.mozilla.org/en-US/docs/Web/JavaScript/Guide/Regular_Expressions */\n\tfunction escapeRegExp(string)\n\t{\n\t\treturn string.replace(/[.*+?^${}()|[\\]\\\\]/g, '\\\\$&'); // $& means the whole matched string\n\t}\n\n\t/** The core parser implements speedy and correct CSV parsing */\n\tfunction Parser(config)\n\t{\n\t\t// Unpack the config object\n\t\tconfig = config || {};\n\t\tvar delim = config.delimiter;\n\t\tvar newline = config.newline;\n\t\tvar comments = config.comments;\n\t\tvar step = config.step;\n\t\tvar preview = config.preview;\n\t\tvar fastMode = config.fastMode;\n\t\tvar quoteChar;\n\t\tvar renamedHeaders = null;\n\t\tvar headerParsed = false;\n\n\t\tif (config.quoteChar === undefined || config.quoteChar === null) {\n\t\t\tquoteChar = '\"';\n\t\t} else {\n\t\t\tquoteChar = config.quoteChar;\n\t\t}\n\t\tvar escapeChar = quoteChar;\n\t\tif (config.escapeChar !== undefined) {\n\t\t\tescapeChar = config.escapeChar;\n\t\t}\n\n\t\t// Delimiter must be valid\n\t\tif (typeof delim !== 'string'\n\t\t\t|| Papa.BAD_DELIMITERS.indexOf(delim) > -1)\n\t\t\tdelim = ',';\n\n\t\t// Comment character must be valid\n\t\tif (comments === delim)\n\t\t\tthrow new Error('Comment character same as delimiter');\n\t\telse if (comments === true)\n\t\t\tcomments = '#';\n\t\telse if (typeof comments !== 'string'\n\t\t\t|| Papa.BAD_DELIMITERS.indexOf(comments) > -1)\n\t\t\tcomments = false;\n\n\t\t// Newline must be valid: \\r, \\n, or \\r\\n\n\t\tif (newline !== '\\n' && newline !== '\\r' && newline !== '\\r\\n')\n\t\t\tnewline = '\\n';\n\n\t\t// We're gonna need these at the Parser scope\n\t\tvar cursor = 0;\n\t\tvar aborted = false;\n\n\t\tthis.parse = function(input, baseIndex, ignoreLastRow)\n\t\t{\n\t\t\t// For some reason, in Chrome, this speeds things up (!?)\n\t\t\tif (typeof input !== 'string')\n\t\t\t\tthrow new Error('Input must be a string');\n\n\t\t\t// We don't need to compute some of these every time parse() is called,\n\t\t\t// but having them in a more local scope seems to perform better\n\t\t\tvar inputLen = input.length,\n\t\t\t\tdelimLen = delim.length,\n\t\t\t\tnewlineLen = newline.length,\n\t\t\t\tcommentsLen = comments.length;\n\t\t\tvar stepIsFunction = isFunction(step);\n\n\t\t\t// Establish starting state\n\t\t\tcursor = 0;\n\t\t\tvar data = [], errors = [], row = [], lastCursor = 0;\n\n\t\t\tif (!input)\n\t\t\t\treturn returnable();\n\n\t\t\tif (fastMode || (fastMode !== false && input.indexOf(quoteChar) === -1))\n\t\t\t{\n\t\t\t\tvar rows = input.split(newline);\n\t\t\t\tfor (var i = 0; i < rows.length; i++)\n\t\t\t\t{\n\t\t\t\t\trow = rows[i];\n\t\t\t\t\tcursor += row.length;\n\n\t\t\t\t\tif (i !== rows.length - 1)\n\t\t\t\t\t\tcursor += newline.length;\n\t\t\t\t\telse if (ignoreLastRow)\n\t\t\t\t\t\treturn returnable();\n\t\t\t\t\tif (comments && row.substring(0, commentsLen) === comments)\n\t\t\t\t\t\tcontinue;\n\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t{\n\t\t\t\t\t\tdata = [];\n\t\t\t\t\t\tpushRow(row.split(delim));\n\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t}\n\t\t\t\t\telse\n\t\t\t\t\t\tpushRow(row.split(delim));\n\t\t\t\t\tif (preview && i >= preview)\n\t\t\t\t\t{\n\t\t\t\t\t\tdata = data.slice(0, preview);\n\t\t\t\t\t\treturn returnable(true);\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn returnable();\n\t\t\t}\n\n\t\t\tvar nextDelim = input.indexOf(delim, cursor);\n\t\t\tvar nextNewline = input.indexOf(newline, cursor);\n\t\t\tvar quoteCharRegex = new RegExp(escapeRegExp(escapeChar) + escapeRegExp(quoteChar), 'g');\n\t\t\tvar quoteSearch = input.indexOf(quoteChar, cursor);\n\n\t\t\t// Parser loop\n\t\t\tfor (;;)\n\t\t\t{\n\t\t\t\t// Field has opening quote\n\t\t\t\tif (input[cursor] === quoteChar)\n\t\t\t\t{\n\t\t\t\t\t// Start our search for the closing quote where the cursor is\n\t\t\t\t\tquoteSearch = cursor;\n\n\t\t\t\t\t// Skip the opening quote\n\t\t\t\t\tcursor++;\n\n\t\t\t\t\tfor (;;)\n\t\t\t\t\t{\n\t\t\t\t\t\t// Find closing quote\n\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, quoteSearch + 1);\n\n\t\t\t\t\t\t//No other quotes are found - no other delimiters\n\t\t\t\t\t\tif (quoteSearch === -1)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tif (!ignoreLastRow) {\n\t\t\t\t\t\t\t\t// No closing quote... what a pity\n\t\t\t\t\t\t\t\terrors.push({\n\t\t\t\t\t\t\t\t\ttype: 'Quotes',\n\t\t\t\t\t\t\t\t\tcode: 'MissingQuotes',\n\t\t\t\t\t\t\t\t\tmessage: 'Quoted field unterminated',\n\t\t\t\t\t\t\t\t\trow: data.length,\t// row has yet to be inserted\n\t\t\t\t\t\t\t\t\tindex: cursor\n\t\t\t\t\t\t\t\t});\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\treturn finish();\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// Closing quote at EOF\n\t\t\t\t\t\tif (quoteSearch === inputLen - 1)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tvar value = input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar);\n\t\t\t\t\t\t\treturn finish(value);\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// If this quote is escaped, it's part of the data; skip it\n\t\t\t\t\t\t// If the quote character is the escape character, then check if the next character is the escape character\n\t\t\t\t\t\tif (quoteChar === escapeChar &&  input[quoteSearch + 1] === escapeChar)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tquoteSearch++;\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\t// If the quote character is not the escape character, then check if the previous character was the escape character\n\t\t\t\t\t\tif (quoteChar !== escapeChar && quoteSearch !== 0 && input[quoteSearch - 1] === escapeChar)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\tcontinue;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tif(nextDelim !== -1 && nextDelim < (quoteSearch + 1)) {\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, (quoteSearch + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t\tif(nextNewline !== -1 && nextNewline < (quoteSearch + 1)) {\n\t\t\t\t\t\t\tnextNewline = input.indexOf(newline, (quoteSearch + 1));\n\t\t\t\t\t\t}\n\t\t\t\t\t\t// Check up to nextDelim or nextNewline, whichever is closest\n\t\t\t\t\t\tvar checkUpTo = nextNewline === -1 ? nextDelim : Math.min(nextDelim, nextNewline);\n\t\t\t\t\t\tvar spacesBetweenQuoteAndDelimiter = extraSpaces(checkUpTo);\n\n\t\t\t\t\t\t// Closing quote followed by delimiter or 'unnecessary spaces + delimiter'\n\t\t\t\t\t\tif (input.substr(quoteSearch + 1 + spacesBetweenQuoteAndDelimiter, delimLen) === delim)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trow.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));\n\t\t\t\t\t\t\tcursor = quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen;\n\n\t\t\t\t\t\t\t// If char after following delimiter is not quoteChar, we find next quote char position\n\t\t\t\t\t\t\tif (input[quoteSearch + 1 + spacesBetweenQuoteAndDelimiter + delimLen] !== quoteChar)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, cursor);\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tvar spacesBetweenQuoteAndNewLine = extraSpaces(nextNewline);\n\n\t\t\t\t\t\t// Closing quote followed by newline or 'unnecessary spaces + newLine'\n\t\t\t\t\t\tif (input.substring(quoteSearch + 1 + spacesBetweenQuoteAndNewLine, quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen) === newline)\n\t\t\t\t\t\t{\n\t\t\t\t\t\t\trow.push(input.substring(cursor, quoteSearch).replace(quoteCharRegex, quoteChar));\n\t\t\t\t\t\t\tsaveRow(quoteSearch + 1 + spacesBetweenQuoteAndNewLine + newlineLen);\n\t\t\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\t// because we may have skipped the nextDelim in the quoted field\n\t\t\t\t\t\t\tquoteSearch = input.indexOf(quoteChar, cursor);\t// we search for first quote in next line\n\n\t\t\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t\t\t{\n\t\t\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t\t\t}\n\n\t\t\t\t\t\t\tif (preview && data.length >= preview)\n\t\t\t\t\t\t\t\treturn returnable(true);\n\n\t\t\t\t\t\t\tbreak;\n\t\t\t\t\t\t}\n\n\n\t\t\t\t\t\t// Checks for valid closing quotes are complete (escaped quotes or quote followed by EOF/delimiter/newline) -- assume these quotes are part of an invalid text string\n\t\t\t\t\t\terrors.push({\n\t\t\t\t\t\t\ttype: 'Quotes',\n\t\t\t\t\t\t\tcode: 'InvalidQuotes',\n\t\t\t\t\t\t\tmessage: 'Trailing quote on quoted field is malformed',\n\t\t\t\t\t\t\trow: data.length,\t// row has yet to be inserted\n\t\t\t\t\t\t\tindex: cursor\n\t\t\t\t\t\t});\n\n\t\t\t\t\t\tquoteSearch++;\n\t\t\t\t\t\tcontinue;\n\n\t\t\t\t\t}\n\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Comment found at start of new line\n\t\t\t\tif (comments && row.length === 0 && input.substring(cursor, cursor + commentsLen) === comments)\n\t\t\t\t{\n\t\t\t\t\tif (nextNewline === -1)\t// Comment ends at EOF\n\t\t\t\t\t\treturn returnable();\n\t\t\t\t\tcursor = nextNewline + newlineLen;\n\t\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// Next delimiter comes before next newline, so we've reached end of field\n\t\t\t\tif (nextDelim !== -1 && (nextDelim < nextNewline || nextNewline === -1))\n\t\t\t\t{\n\t\t\t\t\trow.push(input.substring(cursor, nextDelim));\n\t\t\t\t\tcursor = nextDelim + delimLen;\n\t\t\t\t\t// we look for next delimiter char\n\t\t\t\t\tnextDelim = input.indexOf(delim, cursor);\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\t// End of row\n\t\t\t\tif (nextNewline !== -1)\n\t\t\t\t{\n\t\t\t\t\trow.push(input.substring(cursor, nextNewline));\n\t\t\t\t\tsaveRow(nextNewline + newlineLen);\n\n\t\t\t\t\tif (stepIsFunction)\n\t\t\t\t\t{\n\t\t\t\t\t\tdoStep();\n\t\t\t\t\t\tif (aborted)\n\t\t\t\t\t\t\treturn returnable();\n\t\t\t\t\t}\n\n\t\t\t\t\tif (preview && data.length >= preview)\n\t\t\t\t\t\treturn returnable(true);\n\n\t\t\t\t\tcontinue;\n\t\t\t\t}\n\n\t\t\t\tbreak;\n\t\t\t}\n\n\t\t\treturn finish();\n\n\n\t\t\tfunction pushRow(row)\n\t\t\t{\n\t\t\t\tdata.push(row);\n\t\t\t\tlastCursor = cursor;\n\t\t\t}\n\n\t\t\t/**\n             * checks if there are extra spaces after closing quote and given index without any text\n             * if Yes, returns the number of spaces\n             */\n\t\t\tfunction extraSpaces(index) {\n\t\t\t\tvar spaceLength = 0;\n\t\t\t\tif (index !== -1) {\n\t\t\t\t\tvar textBetweenClosingQuoteAndIndex = input.substring(quoteSearch + 1, index);\n\t\t\t\t\tif (textBetweenClosingQuoteAndIndex && textBetweenClosingQuoteAndIndex.trim() === '') {\n\t\t\t\t\t\tspaceLength = textBetweenClosingQuoteAndIndex.length;\n\t\t\t\t\t}\n\t\t\t\t}\n\t\t\t\treturn spaceLength;\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * Appends the remaining input from cursor to the end into\n\t\t\t * row, saves the row, calls step, and returns the results.\n\t\t\t */\n\t\t\tfunction finish(value)\n\t\t\t{\n\t\t\t\tif (ignoreLastRow)\n\t\t\t\t\treturn returnable();\n\t\t\t\tif (typeof value === 'undefined')\n\t\t\t\t\tvalue = input.substring(cursor);\n\t\t\t\trow.push(value);\n\t\t\t\tcursor = inputLen;\t// important in case parsing is paused\n\t\t\t\tpushRow(row);\n\t\t\t\tif (stepIsFunction)\n\t\t\t\t\tdoStep();\n\t\t\t\treturn returnable();\n\t\t\t}\n\n\t\t\t/**\n\t\t\t * Appends the current row to the results. It sets the cursor\n\t\t\t * to newCursor and finds the nextNewline. The caller should\n\t\t\t * take care to execute user's step function and check for\n\t\t\t * preview and end parsing if necessary.\n\t\t\t */\n\t\t\tfunction saveRow(newCursor)\n\t\t\t{\n\t\t\t\tcursor = newCursor;\n\t\t\t\tpushRow(row);\n\t\t\t\trow = [];\n\t\t\t\tnextNewline = input.indexOf(newline, cursor);\n\t\t\t}\n\n\t\t\t/** Returns an object with the results, errors, and meta. */\n\t\t\tfunction returnable(stopped)\n\t\t\t{\n\t\t\t\tif (config.header && !baseIndex && data.length && !headerParsed)\n\t\t\t\t{\n\t\t\t\t\tconst result = data[0];\n\t\t\t\t\tconst headerCount = Object.create(null); // To track the count of each base header\n\t\t\t\t\tconst usedHeaders = new Set(result); // To track used headers and avoid duplicates\n\t\t\t\t\tlet duplicateHeaders = false;\n\n\t\t\t\t\tfor (let i = 0; i < result.length; i++) {\n\t\t\t\t\t\tlet header = result[i];\n\t\t\t\t\t\tif (isFunction(config.transformHeader))\n\t\t\t\t\t\t\theader = config.transformHeader(header, i);\n\n\t\t\t\t\t\tif (!headerCount[header]) {\n\t\t\t\t\t\t\theaderCount[header] = 1;\n\t\t\t\t\t\t\tresult[i] = header;\n\t\t\t\t\t\t} else {\n\t\t\t\t\t\t\tlet newHeader;\n\t\t\t\t\t\t\tlet suffixCount = headerCount[header];\n\n\t\t\t\t\t\t\t// Find a unique new header\n\t\t\t\t\t\t\tdo {\n\t\t\t\t\t\t\t\tnewHeader = `${header}_${suffixCount}`;\n\t\t\t\t\t\t\t\tsuffixCount++;\n\t\t\t\t\t\t\t} while (usedHeaders.has(newHeader));\n\n\t\t\t\t\t\t\tusedHeaders.add(newHeader); // Mark this new Header as used\n\t\t\t\t\t\t\tresult[i] = newHeader;\n\t\t\t\t\t\t\theaderCount[header]++;\n\t\t\t\t\t\t\tduplicateHeaders = true;\n\t\t\t\t\t\t\tif (renamedHeaders === null) {\n\t\t\t\t\t\t\t\trenamedHeaders = {};\n\t\t\t\t\t\t\t}\n\t\t\t\t\t\t\trenamedHeaders[newHeader] = header;\n\t\t\t\t\t\t}\n\n\t\t\t\t\t\tusedHeaders.add(header); // Ensure the original header is marked as used\n\t\t\t\t\t}\n\t\t\t\t\tif (duplicateHeaders) {\n\t\t\t\t\t\tconsole.warn('Duplicate headers found and renamed.');\n\t\t\t\t\t}\n\t\t\t\t\theaderParsed = true;\n\t\t\t\t}\n\t\t\t\treturn {\n\t\t\t\t\tdata: data,\n\t\t\t\t\terrors: errors,\n\t\t\t\t\tmeta: {\n\t\t\t\t\t\tdelimiter: delim,\n\t\t\t\t\t\tlinebreak: newline,\n\t\t\t\t\t\taborted: aborted,\n\t\t\t\t\t\ttruncated: !!stopped,\n\t\t\t\t\t\tcursor: lastCursor + (baseIndex || 0),\n\t\t\t\t\t\trenamedHeaders: renamedHeaders\n\t\t\t\t\t}\n\t\t\t\t};\n\t\t\t}\n\n\t\t\t/** Executes the user's step function and resets data & errors. */\n\t\t\tfunction doStep()\n\t\t\t{\n\t\t\t\tstep(returnable());\n\t\t\t\tdata = [];\n\t\t\t\terrors = [];\n\t\t\t}\n\t\t};\n\n\t\t/** Sets the abort flag */\n\t\tthis.abort = function()\n\t\t{\n\t\t\taborted = true;\n\t\t};\n\n\t\t/** Gets the cursor position */\n\t\tthis.getCharIndex = function()\n\t\t{\n\t\t\treturn cursor;\n\t\t};\n\t}\n\n\n\tfunction newWorker()\n\t{\n\t\tif (!Papa.WORKERS_SUPPORTED)\n\t\t\treturn false;\n\n\t\tvar workerUrl = getWorkerBlob();\n\t\tvar w = new global.Worker(workerUrl);\n\t\tw.onmessage = mainThreadReceivedMessage;\n\t\tw.id = workerIdCounter++;\n\t\tworkers[w.id] = w;\n\t\treturn w;\n\t}\n\n\t/** Callback when main thread receives a message */\n\tfunction mainThreadReceivedMessage(e)\n\t{\n\t\tvar msg = e.data;\n\t\tvar worker = workers[msg.workerId];\n\t\tvar aborted = false;\n\n\t\tif (msg.error)\n\t\t\tworker.userError(msg.error, msg.file);\n\t\telse if (msg.results && msg.results.data)\n\t\t{\n\t\t\tvar abort = function() {\n\t\t\t\taborted = true;\n\t\t\t\tcompleteWorker(msg.workerId, { data: [], errors: [], meta: { aborted: true } });\n\t\t\t};\n\n\t\t\tvar handle = {\n\t\t\t\tabort: abort,\n\t\t\t\tpause: notImplemented,\n\t\t\t\tresume: notImplemented\n\t\t\t};\n\n\t\t\tif (isFunction(worker.userStep))\n\t\t\t{\n\t\t\t\tfor (var i = 0; i < msg.results.data.length; i++)\n\t\t\t\t{\n\t\t\t\t\tworker.userStep({\n\t\t\t\t\t\tdata: msg.results.data[i],\n\t\t\t\t\t\terrors: msg.results.errors,\n\t\t\t\t\t\tmeta: msg.results.meta\n\t\t\t\t\t}, handle);\n\t\t\t\t\tif (aborted)\n\t\t\t\t\t\tbreak;\n\t\t\t\t}\n\t\t\t\tdelete msg.results;\t// free memory ASAP\n\t\t\t}\n\t\t\telse if (isFunction(worker.userChunk))\n\t\t\t{\n\t\t\t\tworker.userChunk(msg.results, handle, msg.file);\n\t\t\t\tdelete msg.results;\n\t\t\t}\n\t\t}\n\n\t\tif (msg.finished && !aborted)\n\t\t\tcompleteWorker(msg.workerId, msg.results);\n\t}\n\n\tfunction completeWorker(workerId, results) {\n\t\tvar worker = workers[workerId];\n\t\tif (isFunction(worker.userComplete))\n\t\t\tworker.userComplete(results);\n\t\tworker.terminate();\n\t\tdelete workers[workerId];\n\t}\n\n\tfunction notImplemented() {\n\t\tthrow new Error('Not implemented.');\n\t}\n\n\t/** Callback when worker thread receives a message */\n\tfunction workerThreadReceivedMessage(e)\n\t{\n\t\tvar msg = e.data;\n\n\t\tif (typeof Papa.WORKER_ID === 'undefined' && msg)\n\t\t\tPapa.WORKER_ID = msg.workerId;\n\n\t\tif (typeof msg.input === 'string')\n\t\t{\n\t\t\tglobal.postMessage({\n\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\tresults: Papa.parse(msg.input, msg.config),\n\t\t\t\tfinished: true\n\t\t\t});\n\t\t}\n\t\telse if ((global.File && msg.input instanceof File) || msg.input instanceof Object)\t// thank you, Safari (see issue #106)\n\t\t{\n\t\t\tvar results = Papa.parse(msg.input, msg.config);\n\t\t\tif (results)\n\t\t\t\tglobal.postMessage({\n\t\t\t\t\tworkerId: Papa.WORKER_ID,\n\t\t\t\t\tresults: results,\n\t\t\t\t\tfinished: true\n\t\t\t\t});\n\t\t}\n\t}\n\n\t/** Makes a deep copy of an array or object (mostly) */\n\tfunction copy(obj)\n\t{\n\t\tif (typeof obj !== 'object' || obj === null)\n\t\t\treturn obj;\n\t\tvar cpy = Array.isArray(obj) ? [] : {};\n\t\tfor (var key in obj)\n\t\t\tcpy[key] = copy(obj[key]);\n\t\treturn cpy;\n\t}\n\n\tfunction bindFunction(f, self)\n\t{\n\t\treturn function() { f.apply(self, arguments); };\n\t}\n\tfunction isFunction(func)\n\t{\n\t\treturn typeof func === 'function';\n\t}\n\n\treturn Papa;\n}));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/papaparse/papaparse.js\n");

/***/ })

};
;