'use client';

import React, { useState } from 'react';
import { CSVUpload } from '@/components/csv-upload';
import { PNRProcessor } from '@/components/pnr-processor';
import { ResultsTable } from '@/components/results-table';
import { PNRRecord } from '@/types';

export default function Home() {
  const [pnrs, setPnrs] = useState<string[]>([]);
  const [results, setResults] = useState<PNRRecord[]>([]);

  const handlePNRsExtracted = (extractedPnrs: string[]) => {
    setPnrs(extractedPnrs);
    setResults([]); // Clear previous results
  };

  const handleProcessingComplete = (processingResults: PNRRecord[]) => {
    setResults(processingResults);
  };

  return (
    <div className="min-h-screen bg-gray-50">
      <div className="container mx-auto px-4 py-8">
        <div className="mb-8">
          <h1 className="text-3xl font-bold text-gray-900 mb-2">
            Cover Genius Insurance Processor
          </h1>
          <p className="text-gray-600">
            Upload a CSV file with PNR numbers to process insurance data and generate SQL update queries
          </p>
        </div>

        <div className="space-y-6">
          {/* Step 1: CSV Upload */}
          <CSVUpload onPNRsExtracted={handlePNRsExtracted} />

          {/* Step 2: PNR Processing */}
          {pnrs.length > 0 && (
            <PNRProcessor
              pnrs={pnrs}
              onProcessingComplete={handleProcessingComplete}
            />
          )}

          {/* Step 3: Results Display */}
          {results.length > 0 && (
            <ResultsTable results={results} />
          )}
        </div>
      </div>
    </div>
  );
}
