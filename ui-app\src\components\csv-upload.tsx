'use client';

import React, { useState, useCallback } from 'react';
import <PERSON> from 'papapar<PERSON>';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Badge } from '@/components/ui/badge';
import { Upload, FileText, X } from 'lucide-react';

interface CSVUploadProps {
  onPNRsExtracted: (pnrs: string[]) => void;
}

interface CSVData {
  [key: string]: string;
}

export function CSVUpload({ onPNRsExtracted }: CSVUploadProps) {
  const [file, setFile] = useState<File | null>(null);
  const [csvData, setCsvData] = useState<CSVData[]>([]);
  const [headers, setHeaders] = useState<string[]>([]);
  const [selectedColumn, setSelectedColumn] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [pnrs, setPnrs] = useState<string[]>([]);

  const handleFileUpload = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const uploadedFile = event.target.files?.[0];
    if (!uploadedFile) return;

    if (!uploadedFile.name.toLowerCase().endsWith('.csv')) {
      setError('Please upload a CSV file');
      return;
    }

    setFile(uploadedFile);
    setError('');

    // First, try parsing without headers to detect simple PNR list format
    Papa.parse(uploadedFile, {
      header: false,
      skipEmptyLines: true,
      complete: (results) => {
        if (results.errors.length > 0) {
          setError(`CSV parsing error: ${results.errors[0].message}`);
          return;
        }

        const rawData = results.data as string[][];

        // Check if this is a simple PNR list (single column, no headers)
        const isSimplePNRList = rawData.every(row =>
          row.length === 1 &&
          typeof row[0] === 'string' &&
          row[0].trim().length >= 3 &&
          row[0].trim().length <= 10 &&
          /^[A-Z0-9]+$/i.test(row[0].trim())
        );

        if (isSimplePNRList) {
          // Handle simple PNR list format
          const extractedPnrs = rawData
            .map(row => row[0]?.trim().toUpperCase())
            .filter(pnr => pnr && pnr.length >= 3 && pnr.length <= 10)
            .filter((pnr, index, array) => array.indexOf(pnr) === index); // Remove duplicates

          if (extractedPnrs.length === 0) {
            setError('No valid PNR numbers found in the file');
            return;
          }

          setPnrs(extractedPnrs);
          onPNRsExtracted(extractedPnrs);
          setError('');

          // Set dummy data for display purposes
          setCsvData([]);
          setHeaders([]);
          setSelectedColumn('');
        } else {
          // Handle structured CSV format with headers
          Papa.parse(uploadedFile, {
            header: true,
            skipEmptyLines: true,
            complete: (headerResults) => {
              if (headerResults.errors.length > 0) {
                setError(`CSV parsing error: ${headerResults.errors[0].message}`);
                return;
              }

              const data = headerResults.data as CSVData[];
              setCsvData(data);

              if (data.length > 0) {
                const csvHeaders = Object.keys(data[0]);
                setHeaders(csvHeaders);

                // Auto-select PNR column if found
                const pnrColumn = csvHeaders.find(header =>
                  header.toLowerCase().includes('pnr') ||
                  header.toLowerCase().includes('confirmation') ||
                  header.toLowerCase().includes('booking')
                );
                if (pnrColumn) {
                  setSelectedColumn(pnrColumn);
                }
              }
            },
            error: (error) => {
              setError(`Failed to parse CSV: ${error.message}`);
            }
          });
        }
      },
      error: (error) => {
        setError(`Failed to parse CSV: ${error.message}`);
      }
    });
  }, [onPNRsExtracted]);

  const extractPNRs = useCallback(() => {
    if (!selectedColumn || csvData.length === 0) {
      setError('Please select a column containing PNR numbers');
      return;
    }

    const extractedPnrs = csvData
      .map(row => row[selectedColumn]?.trim().toUpperCase())
      .filter(pnr => pnr && pnr.length >= 3 && pnr.length <= 10)
      .filter((pnr, index, array) => array.indexOf(pnr) === index); // Remove duplicates

    if (extractedPnrs.length === 0) {
      setError('No valid PNR numbers found in the selected column');
      return;
    }

    setPnrs(extractedPnrs);
    onPNRsExtracted(extractedPnrs);
    setError('');
  }, [selectedColumn, csvData, onPNRsExtracted]);

  const clearFile = useCallback(() => {
    setFile(null);
    setCsvData([]);
    setHeaders([]);
    setSelectedColumn('');
    setPnrs([]);
    setError('');
  }, []);

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Upload className="h-5 w-5" />
          Upload CSV File
        </CardTitle>
        <CardDescription>
          Upload a CSV file containing PNR numbers. Supports both simple PNR lists (one per line) and structured CSV files with headers.
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-4">
        {!file ? (
          <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
            <FileText className="mx-auto h-12 w-12 text-gray-400 mb-4" />
            <div className="space-y-2">
              <p className="text-sm text-gray-600">
                Choose a CSV file or drag and drop it here
              </p>
              <Input
                type="file"
                accept=".csv"
                onChange={handleFileUpload}
                className="max-w-xs mx-auto"
              />
            </div>
          </div>
        ) : (
          <div className="space-y-4">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span className="text-sm font-medium">{file.name}</span>
                <Badge variant="secondary">
                  {pnrs.length > 0 ? `${pnrs.length} PNRs` : `${csvData.length} rows`}
                </Badge>
              </div>
              <Button variant="ghost" size="sm" onClick={clearFile}>
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Show column selection only for structured CSV files */}
            {headers.length > 0 && pnrs.length === 0 && (
              <div className="space-y-2">
                <label className="text-sm font-medium">
                  Select column containing PNR numbers:
                </label>
                <select
                  value={selectedColumn}
                  onChange={(e) => setSelectedColumn(e.target.value)}
                  className="w-full p-2 border border-gray-300 rounded-md"
                >
                  <option value="">-- Select Column --</option>
                  {headers.map((header) => (
                    <option key={header} value={header}>
                      {header}
                    </option>
                  ))}
                </select>
              </div>
            )}

            {selectedColumn && pnrs.length === 0 && (
              <div className="space-y-2">
                <p className="text-sm text-gray-600">
                  Preview of {selectedColumn} column (first 5 rows):
                </p>
                <div className="bg-gray-50 p-3 rounded-lg">
                  {csvData.slice(0, 5).map((row, index) => (
                    <div key={index} className="text-sm">
                      {row[selectedColumn]}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Show extract button only for structured CSV files that haven't been processed */}
            {headers.length > 0 && pnrs.length === 0 && (
              <Button
                onClick={extractPNRs}
                disabled={!selectedColumn}
                className="w-full"
              >
                Extract PNR Numbers ({selectedColumn ? csvData.length : 0} rows)
              </Button>
            )}

            {/* Show success message for simple PNR list files */}
            {pnrs.length > 0 && headers.length === 0 && (
              <Alert>
                <AlertDescription>
                  ✅ Automatically detected and processed simple PNR list format.
                  Found {pnrs.length} unique PNR numbers.
                </AlertDescription>
              </Alert>
            )}
          </div>
        )}

        {error && (
          <Alert variant="destructive">
            <AlertDescription>{error}</AlertDescription>
          </Alert>
        )}

        {pnrs.length > 0 && (
          <Alert>
            <AlertDescription>
              Successfully extracted {pnrs.length} unique PNR numbers: {pnrs.slice(0, 5).join(', ')}
              {pnrs.length > 5 && ` and ${pnrs.length - 5} more...`}
            </AlertDescription>
          </Alert>
        )}
      </CardContent>
    </Card>
  );
}
