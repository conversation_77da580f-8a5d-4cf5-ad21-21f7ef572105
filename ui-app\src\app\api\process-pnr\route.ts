import { NextRequest, NextResponse } from 'next/server';

// Import functions directly from the application modules
async function processMainFunction(pnr: string) {
  try {
    // Import required functions
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { retrieveAndSavePNRData, fetchCoverGeniusPolicy } = require('../../../../integrations.js');
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const { extractCoverGeniusPolicyIds, processInsuranceData } = require('../../../../functions.js');
    // eslint-disable-next-line @typescript-eslint/no-require-imports
    const moment = require('moment');

    console.log(`Processing PNR: ${pnr}`);

    // Step 1: Retrieve PNR data from FlyDubai API
    console.log('Step 1: Retrieving PNR data from FlyDubai API...');
    const pnrData = await retrieveAndSavePNRData(pnr);
    console.log('✓ PNR data retrieved successfully');

    // Step 2: Extract Cover Genius policy IDs
    console.log('Step 2: Extracting Cover Genius policy IDs...');
    const policyIds = extractCoverGeniusPolicyIds(pnrData);

    if (policyIds.length === 0) {
      console.log('❌ No Cover Genius policy IDs found in PNR data');
      return {
        pnrNumber: pnr,
        policyId: null,
        insuranceRecords: [],
        policyStartDate: null,
        policyEndDate: null,
        summary: {
          totalRecords: 0,
          withConfirmation: 0,
          missingConfirmation: 0,
          withinPolicyPeriod: 0,
        },
        sqlQueries: [],
        error: 'No Cover Genius policy IDs found in PNR data'
      };
    }

    console.log(`✓ Found ${policyIds.length} policy ID(s): ${policyIds.join(', ')}`);

    // Step 3: Process the first policy ID
    const policyId = policyIds[0];
    console.log(`Step 3: Processing policy ID: ${policyId}`);

    // Fetch Cover Genius policy data
    console.log('  - Fetching policy details from Cover Genius API...');
    const policyResponse = await fetchCoverGeniusPolicy(policyId);
    console.log('  ✓ Policy data retrieved successfully');

    // Extract policy dates
    let policyStartDate = null;
    let policyEndDate = null;
    if (policyResponse && policyResponse.data) {
      if (policyResponse.data.quotes && policyResponse.data.quotes.length > 0) {
        const quote = policyResponse.data.quotes[0];
        if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
        if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
      } else if (policyResponse.data.policy) {
        const policy = policyResponse.data.policy;
        if (policy.start_date) policyStartDate = moment(policy.start_date);
        if (policy.end_date) policyEndDate = moment(policy.end_date);
      }
    } else if (policyResponse && policyResponse.quotes && policyResponse.quotes.length > 0) {
      const quote = policyResponse.quotes[0];
      if (quote.policy_start_date) policyStartDate = moment(quote.policy_start_date);
      if (quote.policy_end_date) policyEndDate = moment(quote.policy_end_date);
    }

    if (policyStartDate && policyEndDate) {
      console.log(`  - Policy period: ${policyStartDate.format("YYYY-MM-DD HH:mm:ss")} to ${policyEndDate.format("YYYY-MM-DD HH:mm:ss")}`);
    }

    // Process the insurance data
    console.log('  - Processing insurance data...');
    const processedData = processInsuranceData(pnrData, policyResponse, policyId);
    console.log(`  ✓ Processed ${processedData.insuranceRecords.length} insurance records`);

    // Calculate summary statistics
    const missingConfirmations = processedData.insuranceRecords.filter((record: any) => !record.hasConfirmation);
    const recordsWithMatchingPolicyId = processedData.insuranceRecords.filter((r: any) => r.matchesCoverGeniusPolicyId);
    const recordsWithinPolicyPeriod = processedData.insuranceRecords.filter((r: any) => r.withinPolicyPeriod === true);

    console.log(`  - Records missing confirmation: ${missingConfirmations.length}`);
    console.log(`  - Records matching policy ID: ${recordsWithMatchingPolicyId.length}`);
    console.log(`  - Records within policy period: ${recordsWithinPolicyPeriod.length}`);

    // Generate SQL queries for missing confirmations
    const sqlQueries = missingConfirmations.map((record: any) => ({
      recordNumber: record.recordNumber,
      passenger: record.passengerName,
      query: `UPDATE P_FZ.RESERVATION_SEGS SET INSURANCE_CONF_NUM='${policyId}' WHERE CONFIRMATION_NUM='${pnr}' AND RECORD_NUM=${record.recordNumber};`
    }));

    // Return structured data
    return {
      pnrNumber: pnr,
      policyId,
      insuranceRecords: processedData.insuranceRecords,
      policyStartDate: policyStartDate ? policyStartDate.format('YYYY-MM-DD') : null,
      policyEndDate: policyEndDate ? policyEndDate.format('YYYY-MM-DD') : null,
      summary: {
        totalRecords: processedData.insuranceRecords.length,
        withConfirmation: processedData.insuranceRecords.length - missingConfirmations.length,
        missingConfirmation: missingConfirmations.length,
        withinPolicyPeriod: recordsWithinPolicyPeriod.length,
      },
      sqlQueries
    };

  } catch (error) {
    console.error('Error in processMainFunction:', error);
    console.error('Current working directory:', process.cwd());
    throw error;
  }
}

export async function POST(request: NextRequest) {
  try {
    const { pnr } = await request.json();

    if (!pnr || typeof pnr !== 'string') {
      return NextResponse.json(
        { error: 'PNR is required and must be a string' },
        { status: 400 }
      );
    }

    // Validate PNR format
    const cleanPnr = pnr.trim().toUpperCase();
    if (cleanPnr.length < 3 || cleanPnr.length > 10) {
      return NextResponse.json(
        { error: 'PNR must be between 3 and 10 characters' },
        { status: 400 }
      );
    }

    try {
      const result = await processMainFunction(cleanPnr);
      return NextResponse.json(result);
    } catch (processingError) {
      console.error('Error processing PNR:', processingError);

      return NextResponse.json(
        {
          error: 'Failed to process PNR',
          details: processingError instanceof Error ? processingError.message : 'Unknown processing error'
        },
        { status: 500 }
      );
    }

  } catch (error) {
    console.error('API Error:', error);
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}
