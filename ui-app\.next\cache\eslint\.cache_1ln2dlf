[{"C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\app\\api\\process-pnr\\route.ts": "1", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\app\\layout.tsx": "2", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\app\\page.tsx": "3", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\csv-upload.tsx": "4", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\pnr-processor.tsx": "5", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\results-table.tsx": "6", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\alert.tsx": "7", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\badge.tsx": "8", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\button.tsx": "9", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\card.tsx": "10", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\dialog.tsx": "11", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\input.tsx": "12", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\progress.tsx": "13", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\table.tsx": "14", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\lib\\utils.ts": "15", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\types\\index.ts": "16", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\lib\\main-wrapper.js": "17"}, {"size": 1804, "mtime": 1748360592089, "results": "18", "hashOfConfig": "19"}, {"size": 689, "mtime": 1748357922809, "results": "20", "hashOfConfig": "19"}, {"size": 1640, "mtime": 1748358443885, "results": "21", "hashOfConfig": "19"}, {"size": 6766, "mtime": 1748358285805, "results": "22", "hashOfConfig": "19"}, {"size": 6987, "mtime": 1748358792669, "results": "23", "hashOfConfig": "19"}, {"size": 9515, "mtime": 1748358808299, "results": "24", "hashOfConfig": "19"}, {"size": 1614, "mtime": 1748358173735, "results": "25", "hashOfConfig": "19"}, {"size": 1631, "mtime": 1748358173719, "results": "26", "hashOfConfig": "19"}, {"size": 2123, "mtime": 1748358173622, "results": "27", "hashOfConfig": "19"}, {"size": 1989, "mtime": 1748358173656, "results": "28", "hashOfConfig": "19"}, {"size": 3813, "mtime": 1748358173756, "results": "29", "hashOfConfig": "19"}, {"size": 967, "mtime": 1748358173700, "results": "30", "hashOfConfig": "19"}, {"size": 740, "mtime": 1748358173709, "results": "31", "hashOfConfig": "19"}, {"size": 2448, "mtime": 1748358173690, "results": "32", "hashOfConfig": "19"}, {"size": 166, "mtime": 1748358117431, "results": "33", "hashOfConfig": "19"}, {"size": 1056, "mtime": 1748358257187, "results": "34", "hashOfConfig": "19"}, {"size": 391, "mtime": 1748360633138, "results": "35", "hashOfConfig": "36"}, {"filePath": "37", "messages": "38", "suppressedMessages": "39", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "hn1wwh", {"filePath": "40", "messages": "41", "suppressedMessages": "42", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "43", "messages": "44", "suppressedMessages": "45", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "46", "messages": "47", "suppressedMessages": "48", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "49", "messages": "50", "suppressedMessages": "51", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "52", "messages": "53", "suppressedMessages": "54", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "55", "messages": "56", "suppressedMessages": "57", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "58", "messages": "59", "suppressedMessages": "60", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "61", "messages": "62", "suppressedMessages": "63", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "64", "messages": "65", "suppressedMessages": "66", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "67", "messages": "68", "suppressedMessages": "69", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "70", "messages": "71", "suppressedMessages": "72", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "73", "messages": "74", "suppressedMessages": "75", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "76", "messages": "77", "suppressedMessages": "78", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "79", "messages": "80", "suppressedMessages": "81", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "82", "messages": "83", "suppressedMessages": "84", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "85", "messages": "86", "suppressedMessages": "87", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1okjhh1", "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\app\\api\\process-pnr\\route.ts", [], ["88"], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\app\\layout.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\app\\page.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\csv-upload.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\pnr-processor.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\results-table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\alert.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\badge.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\button.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\card.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\dialog.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\input.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\progress.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\components\\ui\\table.tsx", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\lib\\utils.ts", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\types\\index.ts", [], [], "C:\\Users\\<USER>\\Documents\\Code\\cover-genius-v1\\ui-app\\src\\lib\\main-wrapper.js", [], ["89", "90"], {"ruleId": "91", "severity": 2, "message": "92", "line": 8, "column": 22, "nodeType": "93", "messageId": "94", "endLine": 8, "endColumn": 58, "suppressions": "95"}, {"ruleId": "91", "severity": 2, "message": "92", "line": 3, "column": 14, "nodeType": "93", "messageId": "94", "endLine": 3, "endColumn": 29, "suppressions": "96"}, {"ruleId": "91", "severity": 2, "message": "92", "line": 8, "column": 18, "nodeType": "93", "messageId": "94", "endLine": 8, "endColumn": 35, "suppressions": "97"}, "@typescript-eslint/no-require-imports", "A `require()` style import is forbidden.", "CallExpression", "noRequireImports", ["98"], ["99"], ["100"], {"kind": "101", "justification": "102"}, {"kind": "101", "justification": "102"}, {"kind": "101", "justification": "102"}, "directive", ""]