{"name": "@types/papaparse", "version": "5.3.16", "description": "TypeScript definitions for papaparse", "homepage": "https://github.com/DefinitelyTyped/DefinitelyTyped/tree/master/types/papaparse", "license": "MIT", "contributors": [{"name": "<PERSON>", "githubUsername": "to<PERSON>edro", "url": "https://github.com/torpedro"}, {"name": "<PERSON>", "githubUsername": "rainshen49", "url": "https://github.com/rainshen49"}, {"name": "<PERSON>", "githubUsername": "j<PERSON><PERSON><PERSON>", "url": "https://github.com/jfloff"}, {"name": "<PERSON>", "githubUsername": "johnny<PERSON><PERSON><PERSON>", "url": "https://github.com/johnnyreilly"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/albertorestifo"}, {"name": "<PERSON><PERSON>", "githubUsername": "j<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/jliuhtonen"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "rbarbazz", "url": "https://github.com/rbarbazz"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/peter<PERSON><PERSON><PERSON><PERSON>"}, {"name": "<PERSON>", "githubUsername": "<PERSON><PERSON><PERSON><PERSON>", "url": "https://github.com/emmanuel<PERSON>tier"}, {"name": "Opportunity Liu", "githubUsername": "OpportunityLiu", "url": "https://github.com/OpportunityLiu"}, {"name": "<PERSON><PERSON><PERSON>", "githubUsername": "matsuby", "url": "https://github.com/matsuby"}], "main": "", "types": "index.d.ts", "repository": {"type": "git", "url": "https://github.com/DefinitelyTyped/DefinitelyTyped.git", "directory": "types/papaparse"}, "scripts": {}, "dependencies": {"@types/node": "*"}, "peerDependencies": {}, "typesPublisherContentHash": "08ee1ed4bb8ac547aa664778ba85a6fc4ff5a36a8f056d1264c5515f33ed9482", "typeScriptVersion": "5.1"}